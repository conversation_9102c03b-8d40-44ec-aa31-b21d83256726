#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PROPORTIONS INDEX5 BACCARAT
====================================

Analyse les proportions des 18 valeurs d'INDEX5 dans le dataset baccarat
pour vérifier la distribution et détecter d'éventuels biais.

Les 18 valeurs INDEX5 attendues :
- 0_A_BANKER, 1_A_BANKER
- 0_B_BANKER, 1_B_BANKER  
- 0_C_BANKER, 1_C_BANKER
- 0_A_PLAYER, 1_A_PLAYER
- 0_B_PLAYER, 1_B_PLAYER
- 0_C_PLAYER, 1_C_PLAYER
- 0_A_TIE, 1_A_TIE
- 0_B_TIE, 1_B_TIE
- 0_C_TIE, 1_C_TIE
"""

import json
import os
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Tuple

class AnalyseurProportionsIndex5:
    """Analyseur des proportions INDEX5 dans le dataset baccarat"""
    
    def __init__(self):
        """Initialise l'analyseur"""
        # Définir les 18 valeurs INDEX5 attendues
        self.index5_attendues = [
            # BANKER
            "0_A_BANKER", "1_A_BANKER",
            "0_B_BANKER", "1_B_BANKER", 
            "0_C_BANKER", "1_C_BANKER",
            # PLAYER
            "0_A_PLAYER", "1_A_PLAYER",
            "0_B_PLAYER", "1_B_PLAYER",
            "0_C_PLAYER", "1_C_PLAYER", 
            # TIE
            "0_A_TIE", "1_A_TIE",
            "0_B_TIE", "1_B_TIE",
            "0_C_TIE", "1_C_TIE"
        ]
        
        # Compteurs
        self.compteur_index5 = Counter()
        self.total_mains = 0
        self.mains_valides = 0
        self.mains_dummy = 0
        
        # Statistiques par catégorie
        self.stats_par_sync = defaultdict(int)      # 0 vs 1
        self.stats_par_cards = defaultdict(int)     # A vs B vs C
        self.stats_par_result = defaultdict(int)    # PLAYER vs BANKER vs TIE
        
    def charger_dataset(self, filename: str) -> bool:
        """
        Charge et analyse le dataset JSON
        
        Args:
            filename: Nom du fichier JSON à analyser
            
        Returns:
            bool: True si chargement réussi
        """
        if not os.path.exists(filename):
            print(f"❌ Fichier non trouvé : {filename}")
            return False
            
        print(f"📂 Chargement du dataset : {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # Vérifier la structure
            if 'parties' not in data:
                print("❌ Structure JSON invalide : clé 'parties' manquante")
                return False
                
            # Analyser les métadonnées
            metadata = data.get('metadata', {})
            print(f"📊 Métadonnées :")
            print(f"   • Générateur : {metadata.get('generateur', 'N/A')}")
            print(f"   • Version : {metadata.get('version', 'N/A')}")
            print(f"   • Date génération : {metadata.get('date_generation', 'N/A')}")
            print(f"   • Nombre parties : {metadata.get('nombre_parties', 'N/A')}")
            print(f"   • Hasard cryptographique : {metadata.get('hasard_cryptographique', 'N/A')}")
            
            # Analyser chaque partie
            parties = data['parties']
            print(f"\n🎲 Analyse de {len(parties)} parties...")
            
            for partie in parties:
                self._analyser_partie(partie)
                
            print(f"✅ Dataset chargé avec succès !")
            print(f"   • Total mains analysées : {self.total_mains}")
            print(f"   • Mains valides : {self.mains_valides}")
            print(f"   • Mains dummy ignorées : {self.mains_dummy}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            return False
    
    def _analyser_partie(self, partie: Dict):
        """Analyse une partie spécifique"""
        mains = partie.get('mains', [])
        
        for main in mains:
            self.total_mains += 1
            
            # Ignorer les mains dummy (main_number = null)
            if main.get('main_number') is None:
                self.mains_dummy += 1
                continue
                
            # Récupérer l'INDEX5
            index5 = main.get('index5_combined', '')
            
            if index5 == '':
                continue  # Ignorer les mains sans INDEX5
                
            self.mains_valides += 1
            
            # Compter l'INDEX5
            self.compteur_index5[index5] += 1
            
            # Analyser les composants
            self._analyser_composants_index5(index5)
    
    def _analyser_composants_index5(self, index5: str):
        """Analyse les composants d'un INDEX5"""
        try:
            parts = index5.split('_')
            if len(parts) == 3:
                sync, cards, result = parts
                
                # Compter par catégorie
                self.stats_par_sync[sync] += 1
                self.stats_par_cards[cards] += 1
                self.stats_par_result[result] += 1
                
        except Exception:
            pass  # Ignorer les INDEX5 malformés
    
    def generer_rapport(self, filename_output: str = None):
        """
        Génère un rapport détaillé des proportions INDEX5
        
        Args:
            filename_output: Nom du fichier de sortie (optionnel)
        """
        if filename_output is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename_output = f"rapport_proportions_index5_{timestamp}.txt"
        
        print(f"\n📊 Génération du rapport : {filename_output}")
        
        with open(filename_output, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("RAPPORT PROPORTIONS INDEX5 BACCARAT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total mains analysées : {self.total_mains:,}\n")
            f.write(f"Mains valides : {self.mains_valides:,}\n")
            f.write(f"Mains dummy ignorées : {self.mains_dummy:,}\n\n")
            
            # Vérifier les 18 valeurs attendues
            f.write("VÉRIFICATION DES 18 VALEURS INDEX5 ATTENDUES\n")
            f.write("=" * 50 + "\n")
            
            valeurs_trouvees = set(self.compteur_index5.keys())
            valeurs_attendues = set(self.index5_attendues)
            
            valeurs_manquantes = valeurs_attendues - valeurs_trouvees
            valeurs_inattendues = valeurs_trouvees - valeurs_attendues
            
            f.write(f"✅ Valeurs trouvées : {len(valeurs_trouvees)}/18\n")
            f.write(f"❌ Valeurs manquantes : {len(valeurs_manquantes)}\n")
            f.write(f"⚠️  Valeurs inattendues : {len(valeurs_inattendues)}\n\n")
            
            if valeurs_manquantes:
                f.write("VALEURS MANQUANTES :\n")
                for valeur in sorted(valeurs_manquantes):
                    f.write(f"   • {valeur}\n")
                f.write("\n")
            
            if valeurs_inattendues:
                f.write("VALEURS INATTENDUES :\n")
                for valeur in sorted(valeurs_inattendues):
                    count = self.compteur_index5[valeur]
                    pct = (count / self.mains_valides) * 100
                    f.write(f"   • {valeur} : {count:,} ({pct:.3f}%)\n")
                f.write("\n")
            
            # Proportions détaillées des 18 valeurs
            f.write("PROPORTIONS DÉTAILLÉES DES 18 VALEURS INDEX5\n")
            f.write("=" * 50 + "\n")
            f.write("Valeur INDEX5        | Count      | Pourcentage | Théorique\n")
            f.write("-" * 60 + "\n")
            
            # Calculer les proportions théoriques approximatives
            proportions_theoriques = self._calculer_proportions_theoriques()
            
            for index5 in self.index5_attendues:
                count = self.compteur_index5.get(index5, 0)
                pct_observe = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
                pct_theorique = proportions_theoriques.get(index5, 0)
                
                f.write(f"{index5:20s} | {count:10,d} | {pct_observe:9.3f}% | {pct_theorique:7.3f}%\n")
            
            f.write("\n")
            
            # Analyse par composants
            self._ecrire_analyse_composants(f)
            
            # Classification en 3 groupes
            self._ecrire_classification_groupes(f)
        
        print(f"✅ Rapport généré : {filename_output}")
        return filename_output

    def _calculer_proportions_theoriques(self) -> Dict[str, float]:
        """Calcule les proportions théoriques approximatives"""
        # Probabilités théoriques approximatives
        p_sync = {"0": 0.50, "1": 0.50}
        p_cards = {"A": 0.13, "B": 0.42, "C": 0.45}  # 4, 6, 5 cartes
        p_result = {"PLAYER": 0.458, "BANKER": 0.458, "TIE": 0.084}

        proportions = {}

        for sync in ["0", "1"]:
            for cards in ["A", "B", "C"]:
                for result in ["PLAYER", "BANKER", "TIE"]:
                    index5 = f"{sync}_{cards}_{result}"
                    prob = p_sync[sync] * p_cards[cards] * p_result[result]
                    proportions[index5] = prob * 100  # En pourcentage

        return proportions

    def _ecrire_analyse_composants(self, f):
        """Écrit l'analyse par composants"""
        f.write("ANALYSE PAR COMPOSANTS\n")
        f.write("=" * 30 + "\n")

        # INDEX1 (SYNC)
        f.write("INDEX1 (SYNC/DESYNC) :\n")
        total_sync = sum(self.stats_par_sync.values())
        for sync, count in sorted(self.stats_par_sync.items()):
            pct = (count / total_sync) * 100 if total_sync > 0 else 0
            f.write(f"   • {sync} : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

        # INDEX2 (CARDS)
        f.write("INDEX2 (NOMBRE CARTES) :\n")
        total_cards = sum(self.stats_par_cards.values())
        for cards, count in sorted(self.stats_par_cards.items()):
            pct = (count / total_cards) * 100 if total_cards > 0 else 0
            cartes_nb = {"A": "4 cartes", "B": "6 cartes", "C": "5 cartes"}.get(cards, cards)
            f.write(f"   • {cards} ({cartes_nb}) : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

        # INDEX3 (RESULT)
        f.write("INDEX3 (RÉSULTAT) :\n")
        total_result = sum(self.stats_par_result.values())
        for result, count in sorted(self.stats_par_result.items()):
            pct = (count / total_result) * 100 if total_result > 0 else 0
            f.write(f"   • {result} : {count:,} ({pct:.2f}%)\n")
        f.write("\n")

    def _ecrire_classification_groupes(self, f):
        """Écrit la classification en 3 groupes"""
        f.write("CLASSIFICATION EN 3 GROUPES\n")
        f.write("=" * 30 + "\n")

        # Groupe 1: TIE (rare)
        groupe_tie = []
        total_tie = 0

        # Groupe 2: A + PLAYER/BANKER (modéré)
        groupe_a_pb = []
        total_a_pb = 0

        # Groupe 3: B/C + PLAYER/BANKER (fréquent)
        groupe_bc_pb = []
        total_bc_pb = 0

        for index5 in self.index5_attendues:
            count = self.compteur_index5.get(index5, 0)

            if "_TIE" in index5:
                groupe_tie.append((index5, count))
                total_tie += count
            elif "_A_" in index5 and ("_PLAYER" in index5 or "_BANKER" in index5):
                groupe_a_pb.append((index5, count))
                total_a_pb += count
            elif ("_B_" in index5 or "_C_" in index5) and ("_PLAYER" in index5 or "_BANKER" in index5):
                groupe_bc_pb.append((index5, count))
                total_bc_pb += count

        # Afficher les groupes
        pct_tie = (total_tie / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_a_pb = (total_a_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0
        pct_bc_pb = (total_bc_pb / self.mains_valides) * 100 if self.mains_valides > 0 else 0

        f.write(f"GROUPE 1 - TIE (RARE) : {total_tie:,} ({pct_tie:.2f}%)\n")
        for index5, count in groupe_tie:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 2 - A + PLAYER/BANKER (MODÉRÉ) : {total_a_pb:,} ({pct_a_pb:.2f}%)\n")
        for index5, count in groupe_a_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        f.write(f"GROUPE 3 - B/C + PLAYER/BANKER (FRÉQUENT) : {total_bc_pb:,} ({pct_bc_pb:.2f}%)\n")
        for index5, count in groupe_bc_pb:
            pct = (count / self.mains_valides) * 100 if self.mains_valides > 0 else 0
            f.write(f"   • {index5} : {count:,} ({pct:.3f}%)\n")
        f.write("\n")

        # Résumé des 3 classes
        f.write("RÉSUMÉ DES 3 CLASSES :\n")
        f.write("-" * 25 + "\n")
        f.write(f"• CLASSE RARE (TIE)     : {pct_tie:6.2f}% ({total_tie:,} mains)\n")
        f.write(f"• CLASSE MODÉRÉE (A+PB) : {pct_a_pb:6.2f}% ({total_a_pb:,} mains)\n")
        f.write(f"• CLASSE FRÉQUENTE (BC+PB): {pct_bc_pb:6.2f}% ({total_bc_pb:,} mains)\n")
        f.write(f"• TOTAL                 : {pct_tie + pct_a_pb + pct_bc_pb:6.2f}% ({self.mains_valides:,} mains)\n")


def main():
    """Fonction principale"""
    print("🎯 ANALYSEUR PROPORTIONS INDEX5 BACCARAT")
    print("=" * 50)

    # Nom du fichier dataset
    filename = "dataset_baccarat_lupasco_20250626_044753.json"

    # Créer l'analyseur
    analyseur = AnalyseurProportionsIndex5()

    # Charger et analyser le dataset
    if analyseur.charger_dataset(filename):
        # Générer le rapport
        rapport_file = analyseur.generer_rapport()

        print(f"\n🎉 ANALYSE TERMINÉE !")
        print(f"📊 Rapport détaillé : {rapport_file}")
        print(f"🔍 Consultez le fichier pour voir les proportions exactes des 18 valeurs INDEX5")
    else:
        print("❌ Échec de l'analyse")


if __name__ == "__main__":
    main()
