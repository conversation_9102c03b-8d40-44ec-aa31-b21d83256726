#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VDIFF.py - ANALYSEUR DIFF INDÉPENDANT
=====================================

Fichier Python unique et autonome reproduisant exactement le processus DIFF
de analyse_complete_avec_diff.py et analyseur_transitions_index5.py

FORMULE DIFF CORRIGÉE: DIFF = |ratio_l4 - ratio_l5|
Architecture: 7 niveaux hiérarchiques pour construction optimale

Auteur: Maître de l'Entropie
Date: 2025-06-28
Version: 1.0 - Implémentation complète autonome
"""

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 1 : FONDATIONS - IMPORTS & CONSTANTES
# ═══════════════════════════════════════════════════════════════════════════════

import json
import math
import os
import glob
import datetime
import traceback
import hashlib
import pickle
import mmap
import gc
import numpy as np
from pathlib import Path
from collections import Counter, defaultdict
from multiprocessing import Pool, cpu_count
from itertools import product

# OPTIMISATIONS ULTRA-RAPIDES - IMPOR<PERSON> CONDITIONNELS
try:
    import orjson
    HAS_ORJSON = True
    print("✅ orjson disponible - parsing JSON ultra-rapide activé")
except ImportError:
    HAS_ORJSON = False
    print("⚠️ orjson non disponible - utilisation de json standard")

try:
    import ijson
    HAS_IJSON = True
    print("✅ ijson disponible - streaming activé")
except ImportError:
    HAS_IJSON = False
    print("⚠️ ijson non disponible - mode streaming désactivé")

# OPTIMISATIONS RÉVOLUTIONNAIRES - NUMBA JIT
try:
    from numba import jit, prange, types
    from numba.typed import Dict, List
    HAS_NUMBA = True
    print("🚀 Numba disponible - JIT compilation ULTRA-RAPIDE activée")
except ImportError:
    HAS_NUMBA = False
    print("⚠️ Numba non disponible - optimisations JIT désactivées")

# FONCTIONS NUMBA JIT ULTRA-RAPIDES
if HAS_NUMBA:
    @jit(nopython=True, parallel=True, cache=True)
    def calcul_entropie_shannon_jit(sequence_array):
        """
        Calcul d'entropie Shannon ultra-rapide avec Numba JIT
        Gain estimé : 10-50x par rapport à la version Python pure
        """
        # Compter les occurrences (optimisé JIT)
        counts = np.zeros(18, dtype=np.int32)  # 18 valeurs INDEX5
        for val in sequence_array:
            if 0 <= val < 18:
                counts[val] += 1

        # Calculer l'entropie
        total = len(sequence_array)
        entropie = 0.0

        for count in counts:
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)

        return entropie

    @jit(nopython=True, parallel=True, cache=True)
    def calcul_entropies_batch_jit(sequences_matrix):
        """
        Calcul d'entropies en batch ultra-rapide
        Traite plusieurs séquences simultanément
        """
        nb_sequences = sequences_matrix.shape[0]
        entropies = np.zeros(nb_sequences, dtype=np.float64)

        for i in prange(nb_sequences):
            entropies[i] = calcul_entropie_shannon_jit(sequences_matrix[i])

        return entropies

    @jit(nopython=True, parallel=True, cache=True)
    def calcul_ratios_entropiques_jit(entropies_locales, entropies_globales):
        """
        Calcul des ratios entropiques ultra-rapide
        """
        nb_ratios = len(entropies_locales)
        ratios = np.zeros(nb_ratios, dtype=np.float64)

        for i in prange(nb_ratios):
            if entropies_globales[i] > 0:
                ratios[i] = entropies_locales[i] / entropies_globales[i]
            else:
                ratios[i] = float('inf') if entropies_locales[i] > 0 else 0.0

        return ratios

    print("🚀 Fonctions Numba JIT compilées - Performance ULTRA-RAPIDE activée")

else:
    # Versions fallback sans Numba
    def calcul_entropie_shannon_jit(sequence_array):
        """Version fallback sans JIT"""
        counts = np.bincount(sequence_array, minlength=18)
        probs = counts / len(sequence_array)
        return -np.sum(probs * np.log2(probs + 1e-10))

    def calcul_entropies_batch_jit(sequences_matrix):
        """Version fallback sans JIT"""
        entropies = []
        for sequence in sequences_matrix:
            entropies.append(calcul_entropie_shannon_jit(sequence))
        return np.array(entropies)

    def calcul_ratios_entropiques_jit(entropies_locales, entropies_globales):
        """Version fallback sans JIT"""
        return entropies_locales / (entropies_globales + 1e-10)

    print("⚠️ Versions fallback sans JIT - Performance standard")

# ═══════════════════════════════════════════════════════════════════════════════
# CONSTANTES CRITIQUES
# ═══════════════════════════════════════════════════════════════════════════════

# 18 VALEURS INDEX5_COMBINED COMPLÈTES
INDEX5_COMBINED = [
    '0_A_BANKER', '1_A_BANKER', '0_B_BANKER', '1_B_BANKER', '0_C_BANKER', '1_C_BANKER',
    '0_A_PLAYER', '1_A_PLAYER', '0_B_PLAYER', '1_B_PLAYER', '0_C_PLAYER', '1_C_PLAYER',
    '0_A_TIE', '1_A_TIE', '0_B_TIE', '1_B_TIE', '0_C_TIE', '1_C_TIE'
]

# SEUILS DIFF SIGMA (8 seuils statistiques corrigés)
SEUILS_DIFF_SIGMA = {
    'SIGNAL_PARFAIT_S': 0.207528,     # 69.1% S, moyenne + 2.5σ
    'SIGNAL_EXCELLENT_S': 0.183457,   # 67.3% S, moyenne + 2σ
    'SIGNAL_TRÈS_BON_S': 0.159386,    # 66.8% S, moyenne + 1.5σ
    'SIGNAL_BON_S': 0.135315,         # 62.5% S, moyenne + 1σ
    'SIGNAL_PARFAIT_O': 0.039031,     # 52.3% O, moyenne - 1σ
    'SIGNAL_EXCELLENT_O': 0.087173,   # 53.0% O, moyenne
    'SIGNAL_TRÈS_BON_O': 0.111244,    # 53.0% O, moyenne + 0.5σ
    'SEUIL_DOUTEUX': 0.231599         # Seuil corrigé
}

# 7 TRANCHES RATIOS
TRANCHES_RATIOS = [
    (0.0, 0.3, "ORDRE_TRÈS_FORT"),
    (0.3, 0.5, "ORDRE_FORT"),
    (0.5, 0.7, "ORDRE_MODÉRÉ"),
    (0.7, 0.9, "ÉQUILIBRE"),
    (0.9, 1.1, "CHAOS_MODÉRÉ"),
    (1.1, 1.5, "CHAOS_FORT"),
    (1.5, 10.0, "CHAOS_EXTRÊME")
]

# RÈGLES BCT (Business Card Theory)
REGLES_TRANSITION = {
    'C': 'ALTERNANCE',    # C → alternance 0↔1
    'A': 'CONSERVATION',  # A → conservation 0→0, 1→1
    'B': 'CONSERVATION'   # B → conservation 0→0, 1→1
}

# Variables globales (comme dans le code original)
dataset_path_global = None
nombre_parties_total = 0
donnees_diff_globales = []  # Stockage global des données extraites

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 2 : FONCTIONS UTILITAIRES DE BASE
# ═══════════════════════════════════════════════════════════════════════════════

def calculer_entropie_shannon(sequence):
    """
    Calcule l'entropie Shannon d'une séquence INDEX5
    Formule: H(X) = -∑ p(x) log₂ p(x)
    """
    if not sequence:
        return 0.0
    
    # Compter les occurrences de chaque valeur
    counts = Counter(sequence)
    total = len(sequence)
    
    # Calculer l'entropie
    entropie = 0.0
    for count in counts.values():
        if count > 0:
            p = count / total
            entropie -= p * math.log2(p)
    
    return entropie

def _calculer_variations_ratios(ratios):
    """
    Calcule les variations absolues entre mains consécutives
    CRITIQUE pour diff_l4 et diff_l5 (lignes 2406-2425 analyse)

    Args:
        ratios: Liste des ratios (L4 ou L5)

    Returns:
        list: Liste des variations absolues où variations[i] = |ratio_i - ratio_i-1|
    """
    if len(ratios) < 2:
        return []

    variations = [0.0]  # variations[0] = 0 (pas de variation pour la première main)
    for i in range(1, len(ratios)):
        variation = abs(ratios[i] - ratios[i-1])
        variations.append(variation)  # variations[i] = variation de la main i

    return variations

def est_sequence_valide_bct(sequence):
    """
    Vérifie si une séquence respecte les règles BCT
    Règles: C → Alternance 0↔1, A/B → Conservation 0→0, 1→1
    """
    if len(sequence) < 2:
        return True
    
    for i in range(len(sequence) - 1):
        valeur_courante = sequence[i]
        valeur_suivante = sequence[i + 1]
        
        # Parser les valeurs INDEX5
        parts_courante = valeur_courante.split('_')
        parts_suivante = valeur_suivante.split('_')
        
        if len(parts_courante) != 3 or len(parts_suivante) != 3:
            return False
        
        index1_courant = parts_courante[0]
        index2_courant = parts_courante[1]
        index1_suivant = parts_suivante[0]
        
        # Vérifier les règles BCT
        if index2_courant == 'C':
            # C → Alternance SYNC/DESYNC
            if index1_suivant == index1_courant:
                return False
        else:  # A ou B
            # A,B → Conservation SYNC/DESYNC
            if index1_suivant != index1_courant:
                return False
    
    return True

def detecter_dataset_le_plus_recent():
    """
    Détecte automatiquement le fichier JSON de dataset le plus récent
    et compte le nombre total de parties qu'il contient.
    """
    global dataset_path_global, nombre_parties_total
    
    print("🔍 DÉTECTION AUTOMATIQUE DU DATASET LE PLUS RÉCENT")
    print("=" * 60)
    
    # Chercher tous les fichiers JSON de dataset dans le dossier courant
    pattern_dataset = "dataset_baccarat_lupasco_*.json"
    fichiers_dataset = glob.glob(pattern_dataset)
    
    if not fichiers_dataset:
        print(f"❌ Aucun fichier dataset trouvé avec le pattern: {pattern_dataset}")
        return None, 0
    
    # Trier par date de modification (plus récent en premier)
    fichiers_dataset.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print(f"📁 {len(fichiers_dataset)} fichier(s) dataset trouvé(s):")
    for fichier in fichiers_dataset:
        taille_gb = os.path.getsize(fichier) / (1024**3)
        print(f"   • {os.path.basename(fichier)} ({taille_gb:.2f} GB)")
    
    # Sélectionner le plus récent
    fichier_selectionne = fichiers_dataset[0]
    taille_gb = os.path.getsize(fichier_selectionne) / (1024**3)
    date_modification = datetime.datetime.fromtimestamp(os.path.getmtime(fichier_selectionne))
    
    print(f"\n✅ FICHIER LE PLUS RÉCENT SÉLECTIONNÉ:")
    print(f"   📄 Fichier: {os.path.basename(fichier_selectionne)}")
    print(f"   📊 Taille: {taille_gb:.2f} GB")
    print(f"   🕒 Modifié: {date_modification}")
    
    # Compter le nombre de parties
    print(f"\n🔢 DÉCOMPTE AUTOMATIQUE DES PARTIES...")
    try:
        with open(fichier_selectionne, 'r', encoding='utf-8') as f:
            data = json.load(f)
            nb_parties = len(data.get('parties', []))
        
        print(f"✅ DÉCOMPTE TERMINÉ:")
        print(f"   🎯 Nombre total de parties: {nb_parties:,}")
        print(f"   📈 Données disponibles pour analyse complète")
        
        # Mettre à jour les variables globales
        dataset_path_global = fichier_selectionne
        nombre_parties_total = nb_parties
        
        return fichier_selectionne, nb_parties
    
    except Exception as e:
        print(f"❌ Erreur lors du décompte: {e}")
        return None, 0

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 3 : GÉNÉRATEURS BCT
# ═══════════════════════════════════════════════════════════════════════════════

def generer_transitions_valides(valeur_courante):
    """
    Génère toutes les transitions valides depuis une valeur INDEX5
    selon les règles BCT INDEX1/INDEX2
    """
    # Parser la valeur courante
    parts = valeur_courante.split('_')
    if len(parts) != 3:
        return []
    
    index1_courant = parts[0]
    index2_courant = parts[1]
    
    # Déterminer INDEX1 suivant selon les règles BCT
    if index2_courant == 'C':
        # C → Alternance SYNC/DESYNC
        index1_suivant = '1' if index1_courant == '0' else '0'
    else:  # A ou B
        # A,B → Conservation SYNC/DESYNC
        index1_suivant = index1_courant
    
    # Générer toutes les transitions possibles avec ce INDEX1
    transitions_valides = []
    for index2 in ['A', 'B', 'C']:
        for index3 in ['BANKER', 'PLAYER', 'TIE']:
            transitions_valides.append(f"{index1_suivant}_{index2}_{index3}")
    
    return transitions_valides

def generer_sequences_bct_l4():
    """
    Génère toutes les séquences BCT valides de longueur 4
    Résultat attendu: 13,122 séquences
    """
    print("🔄 Génération séquences BCT L4...")
    sequences_valides = set()
    
    # Générer toutes les séquences possibles de longueur 4
    for seq in product(INDEX5_COMBINED, repeat=4):
        if est_sequence_valide_bct(seq):
            sequences_valides.add(seq)
    
    print(f"✅ {len(sequences_valides):,} séquences L4 générées")
    return list(sequences_valides)

def generer_sequences_bct_l5():
    """
    Génère toutes les séquences BCT valides de longueur 5
    Résultat attendu: 118,098 séquences
    """
    print("🔄 Génération séquences BCT L5...")
    sequences_valides = set()
    
    # Générer toutes les séquences possibles de longueur 5
    for seq in product(INDEX5_COMBINED, repeat=5):
        if est_sequence_valide_bct(seq):
            sequences_valides.add(seq)
    
    print(f"✅ {len(sequences_valides):,} séquences L5 générées")
    return list(sequences_valides)

def generer_signatures_entropiques():
    """
    Génère les signatures entropiques pour toutes les séquences BCT L4 et L5
    """
    print("🔄 Génération signatures entropiques...")
    
    # Générer séquences L4 et L5
    sequences_l4 = generer_sequences_bct_l4()
    sequences_l5 = generer_sequences_bct_l5()
    
    # Calculer signatures L4
    signatures_l4 = {}
    for seq in sequences_l4:
        signatures_l4[seq] = calculer_entropie_shannon(list(seq))
    
    # Calculer signatures L5
    signatures_l5 = {}
    for seq in sequences_l5:
        signatures_l5[seq] = calculer_entropie_shannon(list(seq))
    
    print(f"✅ Signatures générées: {len(signatures_l4):,} L4 + {len(signatures_l5):,} L5")
    return signatures_l4, signatures_l5

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 4 : CLASSES ANALYSEURS (INTÉGRÉES DE analyseur_transitions_index5.py)
# ═══════════════════════════════════════════════════════════════════════════════

class AnalyseurEvolutionEntropique:
    """
    ANALYSEUR ÉVOLUTION ENTROPIQUE INTÉGRÉ
    =====================================

    Reproduit exactement AnalyseurEvolutionEntropique de analyseur_transitions_index5.py
    Lignes 5406-7114 du fichier source
    """

    def __init__(self, dataset_path):
        """Initialisation avec chemin dataset"""
        self.dataset_path = dataset_path
        self.evolutions_entropiques = {}  # Structure clé : partie_id → données entropiques
        self.base_signatures_4 = {}
        self.base_signatures_5 = {}

        print(f"🔬 AnalyseurEvolutionEntropique initialisé")
        print(f"📁 Dataset: {os.path.basename(dataset_path)}")

    def analyser_toutes_parties_entropiques(self, nb_parties_max=None):
        """
        Méthode PRINCIPALE pour analyse entropique complète
        Reproduit analyser_toutes_parties_entropiques() lignes 5479-5561
        Utilise multiprocessing avec 8 cœurs CPU
        """
        return self.charger_et_analyser_toutes_parties(nb_parties_max)

    def charger_et_analyser_toutes_parties(self, nb_parties_max=None):
        """
        Méthode PRINCIPALE pour analyse entropique complète
        Reproduit analyser_toutes_parties_entropiques() lignes 5479-5561
        """
        print("🔄 CHARGEMENT ET ANALYSE ENTROPIQUE...")

        try:
            # Charger le dataset
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)

            parties = dataset.get('parties', [])
            if nb_parties_max:
                parties = parties[:nb_parties_max]

            print(f"📊 Analyse de {len(parties):,} parties")

            # Générer signatures si nécessaire
            if not self.base_signatures_4 or not self.base_signatures_5:
                print("🔧 Génération signatures entropiques...")
                self.base_signatures_4, self.base_signatures_5 = generer_signatures_entropiques()

            # Analyser chaque partie
            parties_reussies = 0
            parties_echouees = 0
            total_mains_analysees = 0

            for i, partie in enumerate(parties):
                try:
                    resultat = self.analyser_partie_entropique(partie)


                    if resultat and 'erreur' not in resultat:
                        partie_id = resultat['partie_id']
                        self.evolutions_entropiques[partie_id] = resultat
                        parties_reussies += 1
                        total_mains_analysees += resultat.get('nb_mains', 0)
                    else:
                        parties_echouees += 1


                except Exception as e:
                    parties_echouees += 1
                    print(f"⚠️ Erreur partie {i+1}: {e}")

                # Affichage progression
                if (i + 1) % 1000 == 0:
                    print(f"   📈 {i+1:,} parties traitées...")

            print(f"✅ ANALYSE ENTROPIQUE TERMINÉE:")
            print(f"   ✅ Parties réussies: {parties_reussies:,}")
            print(f"   ❌ Parties échouées: {parties_echouees:,}")
            print(f"   📊 Total mains analysées: {total_mains_analysees:,}")

            return {
                'parties_reussies': parties_reussies,
                'parties_echouees': parties_echouees,
                'total_mains_analysees': total_mains_analysees
            }

        except Exception as e:
            print(f"❌ Erreur analyse entropique: {e}")
            return {'erreur': str(e)}

    def analyser_partie_entropique(self, partie):
        """
        Analyse entropique d'une partie individuelle
        Reproduit analyser_partie_entropique() lignes 5562-5661
        """
        try:
            partie_number = partie.get('partie_number')
            mains = partie.get('mains', [])



            # Filtrer mains valides
            mains_valides = []
            for main in mains:
                if isinstance(main, dict) and main.get('main_number') is not None:
                    mains_valides.append(main)



            if len(mains_valides) < 5:
                print(f"❌ Partie {partie_number}: Pas assez de mains valides ({len(mains_valides)} < 5)")
                return {'erreur': 'Pas assez de mains valides'}

            # Alignement avec main dummy
            main_dummy = {'index5_combined': ''}
            mains_alignees = [main_dummy] + mains_valides

            # Extraire la séquence complète INDEX5 avec alignement
            sequence_complete = [main['index5_combined'] for main in mains_alignees]

            # Analyser depuis la main 5
            mains_analysees = []
            entropies_l4 = []
            entropies_l5 = []
            ratios_l4 = []
            ratios_l5 = []

            for position_main in range(5, len(mains_alignees)):
                # Extraire séquences avec vérification
                try:
                    sequence_4 = []
                    for j in range(position_main-3, position_main+1):
                        if j < len(mains_alignees) and isinstance(mains_alignees[j], dict):
                            sequence_4.append(mains_alignees[j]['index5_combined'])
                        else:
                            raise Exception(f"Element {j} n'est pas un dict")

                    sequence_5 = []
                    for j in range(position_main-4, position_main+1):
                        if j < len(mains_alignees) and isinstance(mains_alignees[j], dict):
                            sequence_5.append(mains_alignees[j]['index5_combined'])
                        else:
                            raise Exception(f"Element {j} n'est pas un dict")
                except Exception as e:
                    return {'erreur': str(e)}



                # Calculer entropies avec debug
                try:
                    entropie_l4 = self.base_signatures_4.get(tuple(sequence_4), calculer_entropie_shannon(sequence_4))
                    entropie_l5 = self.base_signatures_5.get(tuple(sequence_5), calculer_entropie_shannon(sequence_5))
                except Exception as e:
                    return {'erreur': f'Erreur calcul entropies: {e}'}

                # CORRECTION CRITIQUE: Calculer entropie globale sur toute la séquence [main 1 à main position_main]
                # Reproduit ligne 5615-5616 analyseur_transitions_index5.py
                seq_globale = sequence_complete[1:position_main+1]  # Commencer à index 1 (main 1)
                entropie_globale = calculer_entropie_shannon(seq_globale)


                # Calculer ratios avec protection division par zéro
                if entropie_globale > 0:
                    ratio_l4 = entropie_l4 / entropie_globale
                    ratio_l5 = entropie_l5 / entropie_globale
                else:
                    ratio_l4 = float('inf') if entropie_l4 > 0 else 0.0
                    ratio_l5 = float('inf') if entropie_l5 > 0 else 0.0

                main_analysee = {
                    'position_main': position_main,
                    'entropie_4': entropie_l4,
                    'entropie_5': entropie_l5,
                    'ratio_4_global': ratio_l4,
                    'ratio_5_global': ratio_l5,
                    'entropie_globale': entropie_globale,
                    'index5_reel': mains_alignees[position_main]['index5_combined']
                }

                mains_analysees.append(main_analysee)
                entropies_l4.append(entropie_l4)
                entropies_l5.append(entropie_l5)
                ratios_l4.append(ratio_l4)
                ratios_l5.append(ratio_l5)

            # DEBUG: Vérifier avant le return
            print(f"✅ Partie {partie_number}: {len(mains_analysees)} mains analysées, retour du résultat")

            return {
                'partie_id': f'partie_{partie_number}',
                'nb_mains': len(mains_analysees),
                'mains_analysees': mains_analysees,
                'entropies_l4': entropies_l4,
                'entropies_l5': entropies_l5,
                'ratios_l4': ratios_l4,
                'ratios_l5': ratios_l5,
                'statistiques_partie': {
                    'entropie_moyenne_l4': sum(entropies_l4) / len(entropies_l4) if entropies_l4 else 0,
                    'entropie_moyenne_l5': sum(entropies_l5) / len(entropies_l5) if entropies_l5 else 0
                }
            }

        except Exception as e:
            return {'erreur': str(e)}

class AnalyseurEvolutionRatios:
    """
    ANALYSEUR ÉVOLUTION RATIOS INTÉGRÉ
    =================================

    Reproduit exactement AnalyseurEvolutionRatios de analyseur_transitions_index5.py
    Lignes 6170-7114 du fichier source
    """

    def __init__(self, analyseur_evolution_entropique):
        """
        Initialisation avec instance d'AnalyseurEvolutionEntropique
        Reproduit __init__() lignes 6178-6192
        """
        self.analyseur_base = analyseur_evolution_entropique
        self.evolutions_ratios = {}  # STRUCTURE CLÉE : partie_id → données ratios

        print(f"📊 AnalyseurEvolutionRatios initialisé")
        print(f"🔗 Lié à AnalyseurEvolutionEntropique")

    def analyser_evolution_toutes_parties(self):
        """
        Méthode PRINCIPALE pour analyse ratios
        Reproduit analyser_evolution_toutes_parties() lignes 6193-6217
        """
        return self.charger_et_analyser_toutes_parties()

    def charger_et_analyser_toutes_parties(self):
        """
        Méthode PRINCIPALE pour analyse ratios
        Reproduit analyser_evolution_toutes_parties() lignes 6193-6217
        """
        print("🔄 ANALYSE ÉVOLUTION RATIOS...")

        try:
            parties_traitees = 0
            parties_reussies = 0

            # Parcourir les évolutions entropiques
            for partie_id, evolution_entropique in self.analyseur_base.evolutions_entropiques.items():
                if 'erreur' in evolution_entropique:
                    continue

                try:
                    resultat = self._analyser_evolution_partie(partie_id, evolution_entropique)
                    if resultat and 'erreur' not in resultat:
                        self.evolutions_ratios[partie_id] = resultat
                        parties_reussies += 1

                    parties_traitees += 1

                    # Affichage progression
                    if parties_traitees % 1000 == 0:
                        print(f"   📈 {parties_traitees:,} parties ratios traitées...")

                except Exception as e:
                    print(f"⚠️ Erreur analyse ratios {partie_id}: {e}")

            print(f"✅ ANALYSE RATIOS TERMINÉE:")
            print(f"   📊 Parties traitées: {parties_traitees:,}")
            print(f"   ✅ Parties réussies: {parties_reussies:,}")

            return True

        except Exception as e:
            print(f"❌ Erreur analyse ratios: {e}")
            return False

    def _analyser_evolution_partie(self, partie_id, evolution_entropique):
        """
        Analyse ratios d'une partie spécifique
        Reproduit _analyser_evolution_partie() lignes 6219-6300
        """
        try:
            # Extraire données entropiques
            ratios_l4 = evolution_entropique.get('ratios_l4', [])
            ratios_l5 = evolution_entropique.get('ratios_l5', [])
            mains_analysees = evolution_entropique.get('mains_analysees', [])

            if not ratios_l4 or not ratios_l5 or not mains_analysees:
                return {'erreur': 'Données entropiques incomplètes'}

            # Calculer variations ratios (CRITIQUE pour diff_l4/diff_l5)
            diff_l4_variations = _calculer_variations_ratios(ratios_l4)
            diff_l5_variations = _calculer_variations_ratios(ratios_l5)

            # Extraire index3_resultats AVANT d'analyser patterns S/O
            index3_resultats = []
            for main in mains_analysees:
                index5_reel = main.get('index5_reel', '')
                if '_' in index5_reel:
                    parts = index5_reel.split('_')
                    index3_resultats.append(parts[-1] if len(parts) >= 3 else 'UNKNOWN')
                else:
                    index3_resultats.append('UNKNOWN')

            # Analyser patterns S/O basé sur index3_resultats (CORRECTION CRITIQUE)
            patterns_soe = self._analyser_patterns_so(index3_resultats)

            return {
                'ratios_l4': ratios_l4,
                'ratios_l5': ratios_l5,
                'patterns_soe': patterns_soe,
                'index3_resultats': index3_resultats,
                'diff_l4_variations': diff_l4_variations,  # CRITIQUE pour corrélations
                'diff_l5_variations': diff_l5_variations,  # CRITIQUE pour corrélations
                'tendances': {
                    'ratio_l4_moyen': sum(ratios_l4) / len(ratios_l4) if ratios_l4 else 0,
                    'ratio_l5_moyen': sum(ratios_l5) / len(ratios_l5) if ratios_l5 else 0
                },
                'statistiques': {
                    'nb_patterns_s': patterns_soe.count('S'),
                    'nb_patterns_o': patterns_soe.count('O'),
                    'nb_mains_analysees': len(mains_analysees)
                }
            }

        except Exception as e:
            return {'erreur': str(e)}

    def _analyser_patterns_so(self, index3_resultats):
        """
        Calcule les patterns S (Same), O (Opposite), E (Égalité) pour chaque main
        Reproduit _calculer_patterns_soe() d'analyseur_transitions_index5.py

        Args:
            index3_resultats: Liste des résultats INDEX3 (BANKER/PLAYER/TIE)

        Returns:
            list: Liste des patterns S/O/E où patterns[i] = pattern de la main i
        """
        if len(index3_resultats) < 2:
            return []

        patterns = [None]  # patterns[0] = None (pas de pattern pour main 0)
        dernier_non_tie = None

        for i in range(1, len(index3_resultats)):
            resultat_actuel = index3_resultats[i]
            resultat_precedent = index3_resultats[i-1]

            # Si le résultat actuel est TIE
            if resultat_actuel == 'TIE':
                patterns.append('E')  # patterns[i] = pattern de la main i
                continue

            # Si le résultat précédent est TIE, chercher le dernier non-TIE
            if resultat_precedent == 'TIE':
                # Chercher le dernier résultat non-TIE avant la position i-1
                dernier_non_tie = None
                for j in range(i-2, -1, -1):
                    if index3_resultats[j] != 'TIE':
                        dernier_non_tie = index3_resultats[j]
                        break

                # Si aucun résultat non-TIE trouvé, on ne peut pas déterminer le pattern
                if dernier_non_tie is None:
                    patterns.append('--')  # Indéterminé
                    continue

                # Comparer avec le dernier non-TIE
                if resultat_actuel == dernier_non_tie:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite
            else:
                # Comparaison normale (pas de TIE précédent)
                if resultat_actuel == resultat_precedent:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite

        return patterns

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 5 : FONCTIONS ANALYSE MÉTIER
# ═══════════════════════════════════════════════════════════════════════════════

def extraire_donnees_avec_diff(analyseur_entropique, analyseur_ratios):
    """
    Extrait les données avec calcul DIFF = |ratio_l4 - ratio_l5|
    AVEC diff_l4 et diff_l5 pour corrélations (lignes 1301-1305 analyse)
    Reproduit lignes 396-456 analyse_complete_avec_diff.py
    """
    print("🔄 Extraction données AVEC DIFF...")

    global donnees_diff_globales  # Stockage global comme dans le code original
    donnees_analyse = []
    parties_traitees = 0

    # Parcourir évolutions ratios (comme dans le code original)
    for partie_id, evolution_ratios in analyseur_ratios.evolutions_ratios.items():
        if 'erreur' in evolution_ratios:
            continue

        # Vérifier données nécessaires
        required_keys = ['ratios_l4', 'ratios_l5', 'patterns_soe', 'index3_resultats']
        if not all(key in evolution_ratios for key in required_keys):
            continue

        # 🚨 CORRECTION CRITIQUE: Reproduire EXACTEMENT l'original (lignes 410-416)
        # L'original ne vérifie PAS analyseur_entropique - seulement analyseur_ratios !
        ratios_l4 = evolution_ratios['ratios_l4']
        ratios_l5 = evolution_ratios['ratios_l5']
        patterns = evolution_ratios['patterns_soe']  # Nom original: patterns
        index3 = evolution_ratios['index3_resultats']  # Nom original: index3
        diff_l4_vars = evolution_ratios.get('diff_l4_variations', [])
        diff_l5_vars = evolution_ratios.get('diff_l5_variations', [])

        # 🚨 REPRODUCTION EXACTE: lignes 418-446 analyse_complete_avec_diff.py
        # LOGIQUE PRÉDICTIVE OPTIMALE : i → i+1
        # Utiliser données main i pour prédire pattern i→i+1
        for i in range(len(patterns)):
            if i < len(ratios_l4) and i < len(ratios_l5) and i < len(index3):
                # OPTIMAL : Données main i pour prédire pattern i→i+1
                ratio_l4_main = ratios_l4[i]      # Main i (état actuel)
                ratio_l5_main = ratios_l5[i]      # Main i (état actuel)
                pattern = patterns[i]             # Pattern i→i+1 (prochaine transition)
                index3_main = index3[i]           # Index3 main i

                # Calculer les différentiels si disponibles
                diff_l4 = diff_l4_vars[i] if i < len(diff_l4_vars) else 0.0
                diff_l5 = diff_l5_vars[i] if i < len(diff_l5_vars) else 0.0

                # CALCUL CRITIQUE : DIFF = |L4-L5| (cohérence)
                diff_coherence = abs(ratio_l4_main - ratio_l5_main)

                # 🚨 CORRECTION CRITIQUE: Filtrage EXACT comme l'original
                # Ignorer les patterns E (TIE) pour cette analyse - REPRODUCTION EXACTE ligne 435
                if pattern in ['S', 'O'] and pattern != 'TIE':
                    donnees_analyse.append({
                        'partie_id': partie_id,
                        'main': i + 5,  # Main réelle (FAIT OBJECTIF: analyse commence à main 5)
                        'ratio_l4': ratio_l4_main,
                        'ratio_l5': ratio_l5_main,
                        'diff_l4': diff_l4,
                        'diff_l5': diff_l5,
                        'diff': diff_coherence,  # VARIABLE DIFF AJOUTÉE
                        'pattern': pattern,
                        'index3': index3_main
                    })

        parties_traitees += 1

    # Stockage global pour réutilisation (comme dans le code original)
    donnees_diff_globales = donnees_analyse

    # 🔧 VALIDATION CRITIQUE: Vérifier la distribution des patterns
    patterns_count = {}
    for d in donnees_analyse:
        pattern = d.get('pattern')
        patterns_count[pattern] = patterns_count.get(pattern, 0) + 1

    print(f"✅ {len(donnees_analyse):,} mains avec DIFF extraites")
    print(f"📈 Distribution patterns: S={patterns_count.get('S', 0):,}, O={patterns_count.get('O', 0):,}, E={patterns_count.get('E', 0):,}")
    print(f"📊 {parties_traitees:,} parties traitées")
    print(f"🎯 Points S+O pour analyse: {patterns_count.get('S', 0) + patterns_count.get('O', 0):,}")
    print(f"🔧 Variables diff_l4 et diff_l5 incluses pour corrélations")

    return donnees_analyse

def analyser_tranche_sigma(donnees, nom_condition, conditions_s, conditions_o, metrique='DIFF'):
    """
    Analyse une tranche avec seuils sigma - REPRODUCTION EXACTE
    Reproduit lignes 273-331 analyse_complete_avec_diff.py
    """
    if len(donnees) < 100:  # Seuil significativité
        return

    # 🚨 CORRECTION CRITIQUE: Logique EXACTE comme l'original (lignes 287-298)
    # Les données ne contiennent déjà QUE des patterns S et O (filtrage fait à l'extraction)
    patterns_s = [d for d in donnees if d.get('pattern', d.get('pattern_so', '')) == 'S']
    patterns_o = [d for d in donnees if d.get('pattern', d.get('pattern_so', '')) == 'O']

    total = len(donnees)  # Total des données passées (déjà filtrées S+O)
    nb_s = len(patterns_s)
    nb_o = len(patterns_o)

    if total == 0:
        return

    # Calculer pourcentages EXACTEMENT comme l'original
    pourcentage_s = (nb_s / total) * 100
    pourcentage_o = (nb_o / total) * 100

    # 🚨 CORRECTION: Seuils EXACTS selon la métrique
    if nom_condition.startswith(('L4_', 'L5_')):
        # Seuils analyser_tranche (lignes 1765-1766) pour ratios L4/L5
        seuil_s = 52.0  # Au moins 52% pour S
        seuil_o = 52.0  # Au moins 52% pour O
    else:
        # Seuils analyser_tranche_sigma pour conditions DIFF (lignes 301-310)
        seuil_s = 55.0  # DIFF a des conditions S fortes
        seuil_o = 52.0  # DIFF a des conditions O modérées

    condition_data = {
        'nom': nom_condition,
        'total_cas': total,
        'nb_s': nb_s,
        'nb_o': nb_o,
        'pourcentage_s': pourcentage_s,
        'pourcentage_o': pourcentage_o,
        'force': 'FORTE' if max(pourcentage_s, pourcentage_o) >= 60 else 'MODÉRÉE' if max(pourcentage_s, pourcentage_o) >= 55 else 'FAIBLE',
        'metrique': metrique
    }

    # 🚨 CORRECTION CRITIQUE: Logique exclusive EXACTE (lignes 324-327)
    # Condition doit dépasser le seuil ET être supérieure à l'autre
    if pourcentage_s >= seuil_s and pourcentage_s > pourcentage_o:
        conditions_s.append(condition_data)
    elif pourcentage_o >= seuil_o and pourcentage_o > pourcentage_s:
        conditions_o.append(condition_data)

def analyser_toutes_conditions_avec_diff(donnees):
    """
    Analyse exhaustive des conditions AVEC DIFF
    Reproduit lignes 1156-1240 analyse_complete_processus_diff.txt
    """
    print("🔬 Analyse exhaustive des conditions AVEC DIFF...")

    conditions_s = []
    conditions_o = []

    # ANALYSE 1: DIFF (Cohérence L4/L5) - SEUILS SIGMA
    print("   📊 Analyse DIFF (cohérence L4/L5) - SEUILS SIGMA...")

    tranches_diff_sigma = [
        # Conditions favorisant S (valeurs élevées)
        (SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'], 10.0, "SIGNAL_PARFAIT_S"),        # >= 2.5σ : 69.1% S
        (SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'], SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'], "SIGNAL_EXCELLENT_S"),    # 2σ-2.5σ : 67.3% S
        (SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_S'], SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'], "SIGNAL_TRÈS_BON_S"),     # 1.5σ-2σ : 66.8% S
        (SEUILS_DIFF_SIGMA['SIGNAL_BON_S'], SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_S'], "SIGNAL_BON_S"),          # 1σ-1.5σ : 62.5% S

        # Conditions favorisant O (valeurs moyennes/basses)
        (SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'], SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_O'], "SIGNAL_EXCELLENT_O"),                   # moyenne-moyenne+0.5σ : 53.0% O
        (SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'], SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'], "SIGNAL_PARFAIT_O"),      # moyenne-1σ à moyenne : 52.3% O
        (0.0, SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'], "SIGNAL_TRÈS_PARFAIT_O"),      # < moyenne-1σ : Conditions O très fortes

        # Zone neutre/douteux
        (SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'], 10.0, "SIGNAL_DOUTEUX")       # > 3σ : Abstention
    ]

    for min_val, max_val, nom in tranches_diff_sigma:
        donnees_tranche = [d for d in donnees if min_val <= d['diff'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche_sigma(donnees_tranche, f"DIFF_{nom}", conditions_s, conditions_o, 'DIFF')

    # ANALYSE 2: Ratios L4 par tranches
    print("   📊 Analyse ratios L4...")
    for min_val, max_val, nom in TRANCHES_RATIOS:
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l4'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche_sigma(donnees_tranche, f"L4_{nom}", conditions_s, conditions_o, 'RATIO_L4')

    # ANALYSE 3: Ratios L5 par tranches
    print("   📊 Analyse ratios L5...")
    for min_val, max_val, nom in TRANCHES_RATIOS:
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l5'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche_sigma(donnees_tranche, f"L5_{nom}", conditions_s, conditions_o, 'RATIO_L5')

    # 🚨 ANALYSE 4: COMBINAISONS COMPLEXES AVEC SEUILS SIGMA - REPRODUCTION EXACTE
    print("   📊 Analyse combinaisons DIFF + Ratios - SEUILS SIGMA...")

    # REPRODUCTION EXACTE des combinaisons de analyse_complete_avec_diff.py (lignes 567-598)
    combinaisons_diff_sigma = {
        # Combinaisons ORDRE FORT avec seuils σ
        "ORDRE_FORT_DIFF_PARFAIT_S": lambda d: d['ratio_l4'] < 0.5 and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "ORDRE_FORT_DIFF_EXCELLENT_S": lambda d: d['ratio_l4'] < 0.5 and SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "ORDRE_FORT_DIFF_BON_S": lambda d: d['ratio_l4'] < 0.5 and SEUILS_DIFF_SIGMA['SIGNAL_BON_S'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'],
        "ORDRE_FORT_DIFF_PARFAIT_O": lambda d: d['ratio_l4'] < 0.5 and d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'],
        "ORDRE_FORT_DIFF_DOUTEUX": lambda d: d['ratio_l4'] < 0.5 and d['diff'] > SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'],

        # Combinaisons ORDRE MODÉRÉ avec seuils σ
        "ORDRE_MODÉRÉ_DIFF_PARFAIT_S": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "ORDRE_MODÉRÉ_DIFF_EXCELLENT_O": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'],
        "ORDRE_MODÉRÉ_DIFF_DOUTEUX": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d['diff'] > SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'],

        # Combinaisons ÉQUILIBRE avec seuils σ
        "ÉQUILIBRE_DIFF_PARFAIT_S": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "ÉQUILIBRE_DIFF_EXCELLENT_S": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "ÉQUILIBRE_DIFF_DOUTEUX": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] > SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'],

        # Combinaisons CHAOS avec seuils σ
        "CHAOS_DIFF_PARFAIT_S": lambda d: d['ratio_l4'] > 0.9 and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "CHAOS_DIFF_EXCELLENT_S": lambda d: d['ratio_l4'] > 0.9 and SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "CHAOS_DIFF_DOUTEUX": lambda d: d['ratio_l4'] > 0.9 and d['diff'] > SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'],

        # Combinaisons avec variations et seuils σ (AJOUT CRITIQUE MANQUANT)
        "VARIATIONS_FORTES_DIFF_PARFAIT_S": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "VARIATIONS_FORTES_DIFF_EXCELLENT_S": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "VARIATIONS_FORTES_DIFF_DOUTEUX": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] > SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX'],

        # Combinaisons stabilité avec seuils σ - 🔧 CORRECTION: Ajout conditions O manquantes
        "STABILITÉ_DIFF_PARFAIT_S": lambda d: d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02 and d['diff'] >= SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S'],
        "STABILITÉ_DIFF_EXCELLENT_O": lambda d: d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02 and SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O'],

        # 🔧 AJOUT CRITIQUE: Conditions O manquantes pour atteindre 10 conditions O
        "STABILITÉ_DIFF_PARFAIT_O": lambda d: d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02 and d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'],
        "ORDRE_FORT_DIFF_EXCELLENT_O": lambda d: d['ratio_l4'] < 0.5 and SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O'] <= d['diff'] < SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O']
    }

    # Analyser chaque combinaison (REPRODUCTION EXACTE ligne 600-603)
    for nom, condition in combinaisons_diff_sigma.items():
        donnees_cond = [d for d in donnees if condition(d)]
        if len(donnees_cond) >= 100:
            analyser_tranche_sigma(donnees_cond, f"COMB_{nom}", conditions_s, conditions_o, 'DIFF')

    print(f"✅ Analyse AVEC DIFF terminée: {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")

    return conditions_s, conditions_o

def calculer_correlations_essentielles(donnees):
    """
    Calcule les 6 corrélations essentielles CORRIGÉES
    Reproduit lignes 1304-1407 analyse_complete_avec_diff.py
    AVEC variables diff_l4 et diff_l5 (lignes 1301-1305 analyse)
    """
    print("🔄 Calcul corrélations essentielles...")

    if not donnees:
        return {}

    # 🚨 CORRECTION: Extraire les VRAIES variables diff_l4 et diff_l5
    diff_l4_values = [d['diff_l4'] for d in donnees]  # |entropie_l4 - entropie_globale|
    diff_l5_values = [d['diff_l5'] for d in donnees]  # |entropie_l5 - entropie_globale|
    diff_values = [d['diff'] for d in donnees]        # |ratio_l4 - ratio_l5|
    ratio_l4_values = [d['ratio_l4'] for d in donnees]
    ratio_l5_values = [d['ratio_l5'] for d in donnees]

    def calculer_correlation_pearson(x_values, y_values):
        """
        Calcule la corrélation de Pearson EXACTE
        Formule: r = Σ[(xi - x̄)(yi - ȳ)] / √[Σ(xi - x̄)² × Σ(yi - ȳ)²]
        Reproduit lignes 1790-1808 analyse_complete_processus_diff.txt
        """
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0

        n = len(x_values)

        # Moyennes
        mean_x = sum(x_values) / n
        mean_y = sum(y_values) / n

        # Numérateur: Σ[(xi - x̄)(yi - ȳ)]
        numerateur = sum((x_values[i] - mean_x) * (y_values[i] - mean_y) for i in range(n))

        # Dénominateur: √[Σ(xi - x̄)² × Σ(yi - ȳ)²]
        sum_sq_x = sum((x_values[i] - mean_x) ** 2 for i in range(n))
        sum_sq_y = sum((y_values[i] - mean_y) ** 2 for i in range(n))
        denominateur = math.sqrt(sum_sq_x * sum_sq_y)

        if denominateur == 0:
            return 0.0

        return numerateur / denominateur

    # 🚨 CORRECTION: Calculer les 6 corrélations essentielles COMPLÈTES
    correlations = {
        'diff_l4_avec_diff': calculer_correlation_pearson(diff_l4_values, diff_values),
        'diff_l5_avec_diff': calculer_correlation_pearson(diff_l5_values, diff_values),
        'ratio_l4_avec_l5': calculer_correlation_pearson(ratio_l4_values, ratio_l5_values),
        'diff_l4_avec_diff_l5': calculer_correlation_pearson(diff_l4_values, diff_l5_values),  # 🚨 MANQUANTE
        'ratio_l4_avec_diff': calculer_correlation_pearson(ratio_l4_values, diff_values),      # 🚨 MANQUANTE
        'ratio_l5_avec_diff': calculer_correlation_pearson(ratio_l5_values, diff_values)      # 🚨 MANQUANTE
    }

    # Statistiques
    statistiques = {
        'total_observations': len(donnees),
        'moyenne_diff': sum(diff_values) / len(diff_values) if diff_values else 0,
        'moyenne_ratio_l4': sum(ratio_l4_values) / len(ratio_l4_values) if ratio_l4_values else 0,
        'moyenne_ratio_l5': sum(ratio_l5_values) / len(ratio_l5_values) if ratio_l5_values else 0
    }

    print(f"✅ Corrélations calculées pour {len(donnees):,} observations")
    return {
        'correlations': correlations,
        'statistiques': statistiques,
        'total_observations': len(donnees)
    }

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 6 : GÉNÉRATION RAPPORTS
# ═══════════════════════════════════════════════════════════════════════════════

def generer_tableau_predictif_avec_diff(conditions_s, conditions_o, total_donnees, correlations_stats=None):
    """
    Génère le rapport final avec tableau prédictif
    Reproduit lignes 1788-2047 analyse_complete_avec_diff.py
    """
    print("📝 Génération tableau prédictif avec DIFF...")

    # Créer nom fichier avec timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    nom_fichier = f"tableau_predictif_avec_diff_{timestamp}.txt"

    try:
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("TABLEAU PRÉDICTIF EXHAUSTIF S/O AVEC DIFF\n")
            f.write("=" * 60 + "\n\n")
            f.write("DIFF = |L4-L5| = Indicateur qualité signal prédictif\n")
            f.write(f"Date d'analyse: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Statistiques globales
            f.write("STATISTIQUES GLOBALES\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total points analysés: {total_donnees:,}\n")
            f.write(f"Conditions S identifiées: {len(conditions_s)}\n")
            f.write(f"Conditions O identifiées: {len(conditions_o)}\n\n")

            # 🚨 SEUILS SIGMA STATISTIQUES DÉTAILLÉS (lignes 1394-1405 analyse)
            f.write("SEUILS SIGMA STATISTIQUES (basés sur 5,528,599 points)\n")
            f.write("=" * 55 + "\n")
            f.write(f"• SIGNAL_PARFAIT_S: ≥{SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_S']:.6f} (69.1% S)\n")
            f.write(f"• SIGNAL_EXCELLENT_S: ≥{SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_S']:.6f} (67.3% S)\n")
            f.write(f"• SIGNAL_TRÈS_BON_S: ≥{SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_S']:.6f} (66.8% S)\n")
            f.write(f"• SIGNAL_BON_S: ≥{SEUILS_DIFF_SIGMA['SIGNAL_BON_S']:.6f} (62.5% S)\n")
            f.write(f"• SIGNAL_PARFAIT_O: <{SEUILS_DIFF_SIGMA['SIGNAL_PARFAIT_O']:.6f} (52.3% O)\n")
            f.write(f"• SIGNAL_EXCELLENT_O: <{SEUILS_DIFF_SIGMA['SIGNAL_EXCELLENT_O']:.6f} (53.0% O)\n")
            f.write(f"• SIGNAL_TRÈS_BON_O: <{SEUILS_DIFF_SIGMA['SIGNAL_TRÈS_BON_O']:.6f} (53.0% O)\n")
            f.write(f"• SEUIL_DOUTEUX: >{SEUILS_DIFF_SIGMA['SEUIL_DOUTEUX']:.6f} (zone d'incertitude)\n\n")

            # Signification DIFF
            f.write("SIGNIFICATION DIFF (COHÉRENCE L4/L5)\n")
            f.write("-" * 35 + "\n")
            f.write("• DIFF élevé (>0.15) → Incohérence L4/L5 → Favorise S\n")
            f.write("• DIFF faible (<0.05) → Cohérence L4/L5 → Favorise O\n")
            f.write("• DIFF = |ratio_L4 - ratio_L5| (FORMULE CORRIGÉE)\n")
            f.write("• Plus DIFF est élevé, plus le signal est prédictif pour S\n\n")

            # Conditions S
            if conditions_s:
                f.write("CONDITIONS FAVORISANT S (PLAYER/BANKER)\n")
                f.write("-" * 45 + "\n")
                # Trier par pourcentage S décroissant
                conditions_s_triees = sorted(conditions_s, key=lambda x: x['pourcentage_s'], reverse=True)
                for condition in conditions_s_triees:
                    f.write(f"• {condition['nom']}: {condition['pourcentage_s']:.1f}% ")
                    f.write(f"({condition['nb_s']}/{condition['total_cas']} points)\n")
                f.write("\n")

            # Conditions O
            if conditions_o:
                f.write("CONDITIONS FAVORISANT O (TIE)\n")
                f.write("-" * 35 + "\n")
                # Trier par pourcentage O décroissant
                conditions_o_triees = sorted(conditions_o, key=lambda x: x['pourcentage_o'], reverse=True)
                for condition in conditions_o_triees:
                    f.write(f"• {condition['nom']}: {condition['pourcentage_o']:.1f}% ")
                    f.write(f"({condition['nb_o']}/{condition['total_cas']} points)\n")
                f.write("\n")

            # 🚨 CORRÉLATIONS ESSENTIELLES COMPLÈTES (6 corrélations)
            if correlations_stats and 'correlations' in correlations_stats:
                f.write("CORRÉLATIONS ESSENTIELLES (6 CORRÉLATIONS CRITIQUES)\n")
                f.write("=" * 55 + "\n")
                correlations = correlations_stats['correlations']

                # Corrélations avec descriptions détaillées
                f.write(f"1. diff_l4_avec_diff: {correlations.get('diff_l4_avec_diff', 0):.4f}\n")
                f.write("   → Corrélation entre |entropie_l4 - entropie_globale| et DIFF\n")
                f.write(f"2. diff_l5_avec_diff: {correlations.get('diff_l5_avec_diff', 0):.4f}\n")
                f.write("   → Corrélation entre |entropie_l5 - entropie_globale| et DIFF\n")
                f.write(f"3. ratio_l4_avec_l5: {correlations.get('ratio_l4_avec_l5', 0):.4f}\n")
                f.write("   → Corrélation entre ratio_L4 et ratio_L5 (cohérence)\n")
                f.write(f"4. diff_l4_avec_diff_l5: {correlations.get('diff_l4_avec_diff_l5', 0):.4f}\n")
                f.write("   → Corrélation entre diff_L4 et diff_L5 (variations)\n")
                f.write(f"5. ratio_l4_avec_diff: {correlations.get('ratio_l4_avec_diff', 0):.4f}\n")
                f.write("   → Corrélation entre ratio_L4 et DIFF (impact L4)\n")
                f.write(f"6. ratio_l5_avec_diff: {correlations.get('ratio_l5_avec_diff', 0):.4f}\n")
                f.write("   → Corrélation entre ratio_L5 et DIFF (impact L5)\n\n")

                # Interprétation des corrélations
                f.write("INTERPRÉTATION CORRÉLATIONS\n")
                f.write("-" * 30 + "\n")
                f.write("• r > 0.7: Corrélation forte positive\n")
                f.write("• 0.3 < r < 0.7: Corrélation modérée\n")
                f.write("• -0.3 < r < 0.3: Corrélation faible\n")
                f.write("• r < -0.3: Corrélation négative\n\n")

                # Statistiques descriptives
                stats = correlations_stats.get('statistiques', {})
                if stats:
                    f.write("STATISTIQUES DESCRIPTIVES\n")
                    f.write("-" * 30 + "\n")
                    for nom, valeur in stats.items():
                        f.write(f"{nom}: {valeur:.6f}\n")
                    f.write("\n")

            # Conclusion
            f.write("CONCLUSION\n")
            f.write("-" * 15 + "\n")
            f.write("Analyse DIFF terminée avec succès.\n")
            f.write("Les conditions identifiées peuvent être utilisées pour la prédiction S/O.\n")
            f.write("Vérifier la significativité statistique (≥100 points) avant utilisation.\n")

        print(f"✅ Rapport généré: {nom_fichier}")
        return nom_fichier

    except Exception as e:
        print(f"❌ Erreur génération rapport: {e}")
        return None

def afficher_resultats_avec_diff(conditions_s, conditions_o, correlations_stats=None):
    """
    Affiche les résultats principaux dans la console
    Reproduit lignes 2142-2186 analyse_complete_avec_diff.py
    """
    print("\n" + "=" * 60)
    print("🎯 RÉSULTATS ANALYSE DIFF")
    print("=" * 60)

    print(f"📊 CONDITIONS IDENTIFIÉES:")
    print(f"   • Conditions S (PLAYER/BANKER): {len(conditions_s)}")
    print(f"   • Conditions O (TIE): {len(conditions_o)}")

    # Meilleures conditions S
    if conditions_s:
        conditions_diff_s = [c for c in conditions_s if 'DIFF_' in c['nom']]
        if conditions_diff_s:
            meilleure_s = max(conditions_diff_s, key=lambda x: x['pourcentage_s'])
            print(f"\n🏆 MEILLEURE CONDITION S (DIFF):")
            print(f"   • {meilleure_s['nom']}: {meilleure_s['pourcentage_s']:.1f}%")
            print(f"   • Points analysés: {meilleure_s['total_cas']:,}")

    # Meilleures conditions O
    if conditions_o:
        conditions_diff_o = [c for c in conditions_o if 'DIFF_' in c['nom']]
        if conditions_diff_o:
            meilleure_o = max(conditions_diff_o, key=lambda x: x['pourcentage_o'])
            print(f"\n🏆 MEILLEURE CONDITION O (DIFF):")
            print(f"   • {meilleure_o['nom']}: {meilleure_o['pourcentage_o']:.1f}%")
            print(f"   • Points analysés: {meilleure_o['total_cas']:,}")

    # Corrélation principale
    if correlations_stats:
        correlations = correlations_stats.get('correlations', {})
        if correlations:
            # Trouver la corrélation la plus forte en valeur absolue
            correlation_principale = max(correlations.items(), key=lambda x: abs(x[1]))
            print(f"\n📈 CORRÉLATION PRINCIPALE:")
            print(f"   • {correlation_principale[0]}: {correlation_principale[1]:.4f}")

    print("\n" + "=" * 60)

# ═══════════════════════════════════════════════════════════════════════════════
# NIVEAU 7 : ORCHESTRATION FINALE
# ═══════════════════════════════════════════════════════════════════════════════

def analyser_conditions_predictives_so_avec_diff():
    """
    FONCTION MAIN - Orchestration complète du processus DIFF
    Reproduit le processus complet en 8 étapes
    """
    print("DEMARRAGE ANALYSE DIFF INDEPENDANTE")
    print("=" * 60)
    print("FORMULE DIFF: |ratio_l4 - ratio_l5|")
    print("Architecture: 7 niveaux hiérarchiques")
    print("=" * 60)

    try:
        # ÉTAPE 1: Détection et chargement dataset
        print("\n📁 ÉTAPE 1: DÉTECTION DATASET")
        dataset_path, nb_parties = detecter_dataset_le_plus_recent()
        if not dataset_path:
            print("❌ Aucun dataset trouvé")
            return False

        # ÉTAPE 2: Initialisation analyseurs internes
        print("\n🔧 ÉTAPE 2: INITIALISATION ANALYSEURS")
        analyseur_entropique = AnalyseurEvolutionEntropique(dataset_path)

        # ÉTAPE 3: Analyse entropique complète
        print("\n📊 ÉTAPE 3: ANALYSE ENTROPIQUE")
        success = analyseur_entropique.analyser_toutes_parties_entropiques(nb_parties_max=nb_parties)
        if not success:
            print("❌ Erreur analyse entropique")
            return False

        # ÉTAPE 4: Analyse ratios
        print("\n📈 ÉTAPE 4: ANALYSE RATIOS")
        analyseur_ratios = AnalyseurEvolutionRatios(analyseur_entropique)
        success = analyseur_ratios.analyser_evolution_toutes_parties()
        if not success:
            print("❌ Erreur analyse ratios")
            return False

        # ÉTAPE 5: Extraction données avec DIFF
        print("\n🔍 ÉTAPE 5: EXTRACTION DONNÉES DIFF")
        donnees_analyse = extraire_donnees_avec_diff(analyseur_entropique, analyseur_ratios)
        if not donnees_analyse:
            print("❌ Aucune donnée extraite")
            return False

        # ÉTAPE 6: Analyse exhaustive conditions
        print("\n🔬 ÉTAPE 6: ANALYSE CONDITIONS")
        conditions_s, conditions_o = analyser_toutes_conditions_avec_diff(donnees_analyse)

        # ÉTAPE 7: Calcul corrélations
        print("\n📊 ÉTAPE 7: CORRÉLATIONS")
        correlations_stats = calculer_correlations_essentielles(donnees_analyse)

        # ÉTAPE 8: Génération rapport et affichage
        print("\n📝 ÉTAPE 8: RAPPORT FINAL")
        nom_fichier = generer_tableau_predictif_avec_diff(
            conditions_s, conditions_o, len(donnees_analyse), correlations_stats
        )

        # Affichage résultats
        afficher_resultats_avec_diff(conditions_s, conditions_o, correlations_stats)

        if nom_fichier:
            print(f"\n✅ ANALYSE DIFF TERMINÉE AVEC SUCCÈS")
            print(f"📄 Rapport généré: {nom_fichier}")
            return True
        else:
            print("❌ Erreur génération rapport")
            return False

    except Exception as e:
        print(f"❌ ERREUR CRITIQUE: {e}")
        traceback.print_exc()
        return False

# ═══════════════════════════════════════════════════════════════════════════════
# POINT D'ENTRÉE PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

if __name__ == "__main__":
    print("VDIFF.py - ANALYSEUR DIFF INDÉPENDANT v1.0")
    print("Maître de l'Entropie - 2025-06-28")
    print()

    success = analyser_conditions_predictives_so_avec_diff()

    if success:
        print("\n🎉 MISSION ACCOMPLIE!")
        print("L'analyse DIFF indépendante s'est terminée avec succès.")
    else:
        print("\n💥 MISSION ÉCHOUÉE!")
        print("L'analyse DIFF a rencontré des erreurs.")

    print("\nFin du programme VDIFF.py")

if __name__ == "__main__":
    analyser_conditions_predictives_so_avec_diff()
