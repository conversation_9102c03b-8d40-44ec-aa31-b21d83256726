#!/usr/bin/env python3
"""
🚀 PRÉDICTEUR INDEX5 ULTRA-RAPIDE
Optimisé pour 8 cœurs + 28GB RAM - SANS GOULOTS D'ÉTRANGLEMENT
Basé sur règles déterministes INDEX1 + déséquilibres catégoriels
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import time
import os
from datetime import datetime
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# 🚀 OPTIMISATIONS ULTRA-RAPIDES - IMPORTS CONDITIONNELS (de VDIFF.py)
try:
    import orjson
    HAS_ORJSON = True
    print("✅ orjson disponible - chargement JSON 3x plus rapide")
except ImportError:
    HAS_ORJSON = False
    print("⚠️  orjson non disponible - utilisation json standard")

try:
    import ijson
    HAS_IJSON = True
    print("✅ ijson disponible - streaming JSON pour gros fichiers")
except ImportError:
    HAS_IJSON = False
    print("⚠️  ijson non disponible - chargement complet en mémoire")

# Configuration optimisation
CPU_COUNT = mp.cpu_count()
MAX_WORKERS = min(CPU_COUNT, 8)
CHUNK_SIZE = 100000  # Chunks plus gros pour moins d'overhead

print(f"🚀 PRÉDICTEUR INDEX5 ULTRA-RAPIDE - {MAX_WORKERS} cœurs")

class PredicteurIndex5Rapide:
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        
        # Règles déterministes INDEX1 (intrinsèques au système)
        self.regles_index1 = {
            'C': 'INVERSION',    # 0→1, 1→0
            'A': 'CONSERVATION', # 0→0, 1→1  
            'B': 'CONSERVATION'  # 0→0, 1→1
        }
        
        # Déséquilibres catégoriels découverts (constants)
        self.desequilibres = {
            'C': {'favori': 'BANKER', 'avantage': 12.1},  # +12.1% BANKER
            'B': {'favori': 'PLAYER', 'avantage': 7.9},   # +7.9% PLAYER
            'A': {'favori': 'EQUILIBRE', 'avantage': 0.0}  # Équilibre
        }
        
        print(f"📂 Dataset: {dataset_path}")
        print(f"🚨 Règles INDEX1: C=INVERSION, A/B=CONSERVATION")
        print(f"📊 Déséquilibres: C→BANKER(+12.1%), B→PLAYER(+7.9%)")

    def predire_index1_suivant(self, index1_actuel: int, categorie: str) -> int:
        """🚨 PRÉDICTION DÉTERMINISTE INDEX1"""
        if categorie == 'C':
            return 1 - index1_actuel  # INVERSION
        else:  # A ou B
            return index1_actuel      # CONSERVATION

    def calculer_prediction_rapide(self, sync_state: int, categorie: str) -> Tuple[str, float]:
        """
        🚀 PRÉDICTION ULTRA-RAPIDE - PURE LOGIQUE DÉTERMINISTE
        """
        # Prédire INDEX1 suivant
        index1_predit = self.predire_index1_suivant(sync_state, categorie)
        
        # Score basé sur déséquilibres catégoriels
        if categorie == 'C':
            # Catégorie C → BANKER favorisé (+12.1%)
            score = 20.0
            if index1_predit == 1:  # 1_C_* encore plus fort
                score += 5.0
            return 'BANKER', score
            
        elif categorie == 'B':
            # Catégorie B → PLAYER favorisé (+7.9%)
            score = 15.0
            if index1_predit == 1:  # 1_B_* renforcé
                score += 3.0
            return 'PLAYER', score
            
        else:  # Catégorie A - équilibre
            # Utiliser INDEX1 comme tie-breaker
            if index1_predit == 0:
                return 'BANKER', 1.0
            else:
                return 'PLAYER', 1.0

    def charger_dataset(self) -> Dict:
        """🚀 CHARGEMENT ULTRA-OPTIMISÉ avec orjson/ijson"""
        print("🔄 Chargement dataset ultra-optimisé...")

        taille_gb = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille: {taille_gb:.2f} GB")

        debut_chargement = time.time()

        # Méthode 1: orjson (3x plus rapide que json standard)
        if HAS_ORJSON:
            print("🚀 Utilisation orjson (ultra-rapide)...")
            with open(self.dataset_path, 'rb') as f:  # Mode binaire pour orjson
                dataset = orjson.loads(f.read())

        # Méthode 2: json standard optimisé
        else:
            print("📦 Utilisation json standard...")
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)

        temps_chargement = time.time() - debut_chargement
        print(f"✅ {dataset['metadata']['nombre_parties']:,} parties chargées en {temps_chargement:.1f}s")
        print(f"🚀 Vitesse: {taille_gb/temps_chargement:.1f} GB/s")

        return dataset

    def extraire_donnees_streaming(self) -> pd.DataFrame:
        """🚀 EXTRACTION STREAMING - ÉVITE CHARGEMENT COMPLET EN MÉMOIRE"""
        if not HAS_IJSON:
            # Fallback sur méthode classique
            dataset = self.charger_dataset()
            return self.extraire_donnees_rapide(dataset)

        print("🌊 Extraction streaming avec ijson...")
        debut = time.time()

        donnees_totales = []
        parties_traitees = 0

        # Streaming JSON - traitement partie par partie
        with open(self.dataset_path, 'rb') as f:
            parser = ijson.parse(f)
            partie_courante = {}
            mains_courantes = []
            main_courante = {}

            for prefix, event, value in parser:
                if prefix.startswith('parties.item') and event == 'start_map':
                    partie_courante = {}
                    mains_courantes = []

                elif prefix.endswith('.partie_number') and event == 'number':
                    partie_courante['partie_number'] = value

                elif prefix.endswith('.mains.item') and event == 'start_map':
                    main_courante = {}

                elif prefix.endswith('.main_number') and event == 'number':
                    if 'main_courante' in locals():
                        main_courante['main_number'] = value

                elif prefix.endswith('.index1_sync_state') and event == 'number':
                    if 'main_courante' in locals():
                        main_courante['index1_sync_state'] = value

                elif prefix.endswith('.index2_cards_category') and event == 'string':
                    if 'main_courante' in locals():
                        main_courante['index2_cards_category'] = value

                elif prefix.endswith('.index3_result') and event == 'string':
                    if 'main_courante' in locals():
                        main_courante['index3_result'] = value

                elif prefix.endswith('.mains.item') and event == 'end_map':
                    if main_courante:
                        mains_courantes.append(main_courante.copy())

                elif prefix.startswith('parties.item') and event == 'end_map':
                    # Traiter la partie complète
                    if mains_courantes and 'partie_number' in partie_courante:
                        donnees_partie = self._extraire_partie_streaming(
                            partie_courante['partie_number'],
                            mains_courantes
                        )
                        donnees_totales.extend(donnees_partie)
                        parties_traitees += 1

                        if parties_traitees % 1000 == 0:
                            print(f"   📊 {parties_traitees:,} parties traitées...")

        df = pd.DataFrame(donnees_totales)
        temps = time.time() - debut
        print(f"✅ {len(df):,} observations extraites en {temps:.1f}s (streaming)")
        return df

    def extraire_donnees_rapide(self, dataset: Dict) -> pd.DataFrame:
        """Extraction ultra-rapide avec multiprocessing"""
        print("🔍 Extraction données (multiprocessing)...")

        parties = dataset['parties']
        chunk_size = max(1000, len(parties) // MAX_WORKERS)
        chunks = [parties[i:i + chunk_size] for i in range(0, len(parties), chunk_size)]

        print(f"📦 {len(chunks)} chunks pour {MAX_WORKERS} workers")

        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [executor.submit(self._extraire_chunk_rapide, chunk) for chunk in chunks]

            donnees_totales = []
            for future in as_completed(futures):
                chunk_donnees = future.result()
                donnees_totales.extend(chunk_donnees)

        df = pd.DataFrame(donnees_totales)
        print(f"✅ {len(df):,} observations extraites")
        return df

    def _extraire_partie_streaming(self, partie_id: int, mains: List) -> List[Dict]:
        """Extraction d'une partie en mode streaming"""
        donnees = []
        mains_valides = [m for m in mains if m.get('main_number') is not None]

        for i in range(1, len(mains_valides)):
            main_precedente = mains_valides[i-1]
            main_actuelle = mains_valides[i]

            # Exclure TIE du target
            if main_actuelle.get('index3_result') in ['BANKER', 'PLAYER']:
                donnees.append({
                    'partie_id': partie_id,
                    'main_n': main_precedente['main_number'],
                    'sync_state_n': main_precedente['index1_sync_state'],
                    'categorie_n': main_precedente['index2_cards_category'],
                    'target': 1 if main_actuelle['index3_result'] == 'BANKER' else 0
                })

        return donnees

    def _extraire_chunk_rapide(self, parties_chunk: List) -> List[Dict]:
        """Worker pour extraction rapide"""
        donnees = []

        for partie in parties_chunk:
            mains_valides = [m for m in partie['mains'] if m['main_number'] is not None]

            for i in range(1, len(mains_valides)):  # Commencer à 1
                main_precedente = mains_valides[i-1]
                main_actuelle = mains_valides[i]

                # Exclure TIE du target
                if main_actuelle['index3_result'] in ['BANKER', 'PLAYER']:
                    donnees.append({
                        'partie_id': partie['partie_number'],
                        'main_n': main_precedente['main_number'],
                        'sync_state_n': main_precedente['index1_sync_state'],
                        'categorie_n': main_precedente['index2_cards_category'],
                        'target': 1 if main_actuelle['index3_result'] == 'BANKER' else 0
                    })

        return donnees

    def executer_predictions_rapide(self, df: pd.DataFrame) -> pd.DataFrame:
        """🚀 PRÉDICTIONS VECTORISÉES ULTRA-RAPIDES"""
        print("🎯 Calcul prédictions vectorisées...")
        
        # Vectorisation pure - pas de multiprocessing pour éviter overhead
        predictions = []
        scores = []
        
        total = len(df)
        batch_size = 100000
        
        for i in range(0, total, batch_size):
            batch = df.iloc[i:i+batch_size]
            
            batch_pred = []
            batch_scores = []
            
            # Vectorisation par catégorie
            for _, row in batch.iterrows():
                pred, score = self.calculer_prediction_rapide(
                    row['sync_state_n'], 
                    row['categorie_n']
                )
                batch_pred.append(pred)
                batch_scores.append(score)
            
            predictions.extend(batch_pred)
            scores.extend(batch_scores)
            
            if (i // batch_size + 1) % 10 == 0:
                print(f"   📊 {i+len(batch):,}/{total:,} prédictions...")
        
        df['prediction'] = predictions
        df['score'] = scores
        df['prediction_numeric'] = df['prediction'].map({'BANKER': 1, 'PLAYER': 0})
        
        print(f"✅ {len(predictions):,} prédictions calculées")
        return df

    def evaluer_performance(self, df: pd.DataFrame) -> Dict:
        """Évaluation performance"""
        print("📈 Évaluation performance...")
        
        # Accuracy globale
        accuracy = (df['prediction_numeric'] == df['target']).mean()
        
        # Par catégorie
        stats_cat = {}
        for cat in ['A', 'B', 'C']:
            mask = df['categorie_n'] == cat
            if mask.sum() > 0:
                acc_cat = (df[mask]['prediction_numeric'] == df[mask]['target']).mean()
                stats_cat[cat] = {
                    'count': mask.sum(),
                    'accuracy': acc_cat,
                    'predictions': df[mask]['prediction'].value_counts().to_dict()
                }
        
        # Par prédiction
        stats_pred = {}
        for pred in ['BANKER', 'PLAYER']:
            mask = df['prediction'] == pred
            if mask.sum() > 0:
                acc_pred = (df[mask]['prediction_numeric'] == df[mask]['target']).mean()
                stats_pred[pred] = {
                    'count': mask.sum(),
                    'accuracy': acc_pred
                }
        
        resultats = {
            'accuracy_globale': accuracy,
            'total_predictions': len(df),
            'par_categorie': stats_cat,
            'par_prediction': stats_pred
        }
        
        return resultats

    def generer_rapport(self, resultats: Dict, temps_execution: float):
        """Génération rapport final"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_rapport = f"rapport_predicteur_index5_rapide_{timestamp}.txt"
        
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write("🚀 RAPPORT PRÉDICTEUR INDEX5 ULTRA-RAPIDE\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"⏱️  TEMPS EXÉCUTION: {temps_execution:.1f} secondes\n")
            f.write(f"📊 TOTAL PRÉDICTIONS: {resultats['total_predictions']:,}\n")
            f.write(f"🎯 ACCURACY GLOBALE: {resultats['accuracy_globale']:.3f} ({resultats['accuracy_globale']*100:.1f}%)\n\n")
            
            f.write("📈 PERFORMANCE PAR CATÉGORIE:\n")
            f.write("-" * 30 + "\n")
            for cat, stats in resultats['par_categorie'].items():
                f.write(f"Catégorie {cat}: {stats['accuracy']:.3f} ({stats['accuracy']*100:.1f}%) - {stats['count']:,} obs\n")
                for pred, count in stats['predictions'].items():
                    f.write(f"  → {pred}: {count:,}\n")
            
            f.write(f"\n🎯 PERFORMANCE PAR PRÉDICTION:\n")
            f.write("-" * 30 + "\n")
            for pred, stats in resultats['par_prediction'].items():
                f.write(f"{pred}: {stats['accuracy']:.3f} ({stats['accuracy']*100:.1f}%) - {stats['count']:,} prédictions\n")
        
        print(f"📄 Rapport sauvegardé: {nom_rapport}")
        return nom_rapport

def main():
    """🚀 FONCTION PRINCIPALE ULTRA-OPTIMISÉE"""
    debut = time.time()

    print("🚀 DÉMARRAGE PRÉDICTEUR INDEX5 ULTRA-RAPIDE")
    print("=" * 50)

    # Initialisation
    predicteur = PredicteurIndex5Rapide("dataset_baccarat_lupasco_20250627_090239.json")

    # 🚀 EXTRACTION OPTIMISÉE - Choisir la meilleure méthode
    taille_gb = os.path.getsize(predicteur.dataset_path) / (1024**3)

    # 🚀 TOUJOURS UTILISER orjson + multiprocessing (plus simple et rapide)
    print(f"📊 Fichier {taille_gb:.1f}GB → orjson + multiprocessing")
    dataset = predicteur.charger_dataset()
    df_donnees = predicteur.extraire_donnees_rapide(dataset)

    # Prédictions
    df_predictions = predicteur.executer_predictions_rapide(df_donnees)

    # Évaluation
    resultats = predicteur.evaluer_performance(df_predictions)

    # Temps total
    temps_total = time.time() - debut

    # Rapport
    rapport = predicteur.generer_rapport(resultats, temps_total)

    print(f"\n🎉 TERMINÉ EN {temps_total:.1f} SECONDES!")
    print(f"🎯 Accuracy: {resultats['accuracy_globale']*100:.1f}%")
    print(f"📄 Rapport: {rapport}")
    print(f"🚀 Performance: {len(df_predictions)/temps_total:.0f} prédictions/seconde")

if __name__ == "__main__":
    main()
