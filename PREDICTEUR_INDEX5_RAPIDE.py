#!/usr/bin/env python3
"""
🚀 PRÉDICTEUR INDEX5 ULTRA-RAPIDE
Optimisé pour 8 cœurs + 28GB RAM - SANS GOULOTS D'ÉTRANGLEMENT
Basé sur règles déterministes INDEX1 + déséquilibres catégoriels
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import time
import os
from datetime import datetime
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Configuration optimisation
CPU_COUNT = mp.cpu_count()
MAX_WORKERS = min(CPU_COUNT, 8)
CHUNK_SIZE = 100000  # Chunks plus gros pour moins d'overhead

print(f"🚀 PRÉDICTEUR INDEX5 ULTRA-RAPIDE - {MAX_WORKERS} cœurs")

class PredicteurIndex5Rapide:
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        
        # Règles déterministes INDEX1 (intrinsèques au système)
        self.regles_index1 = {
            'C': 'INVERSION',    # 0→1, 1→0
            'A': 'CONSERVATION', # 0→0, 1→1  
            'B': 'CONSERVATION'  # 0→0, 1→1
        }
        
        # Déséquilibres catégoriels découverts (constants)
        self.desequilibres = {
            'C': {'favori': 'BANKER', 'avantage': 12.1},  # +12.1% BANKER
            'B': {'favori': 'PLAYER', 'avantage': 7.9},   # +7.9% PLAYER
            'A': {'favori': 'EQUILIBRE', 'avantage': 0.0}  # Équilibre
        }
        
        print(f"📂 Dataset: {dataset_path}")
        print(f"🚨 Règles INDEX1: C=INVERSION, A/B=CONSERVATION")
        print(f"📊 Déséquilibres: C→BANKER(+12.1%), B→PLAYER(+7.9%)")

    def predire_index1_suivant(self, index1_actuel: int, categorie: str) -> int:
        """🚨 PRÉDICTION DÉTERMINISTE INDEX1"""
        if categorie == 'C':
            return 1 - index1_actuel  # INVERSION
        else:  # A ou B
            return index1_actuel      # CONSERVATION

    def calculer_prediction_rapide(self, sync_state: int, categorie: str) -> Tuple[str, float]:
        """
        🚀 PRÉDICTION ULTRA-RAPIDE - PURE LOGIQUE DÉTERMINISTE
        """
        # Prédire INDEX1 suivant
        index1_predit = self.predire_index1_suivant(sync_state, categorie)
        
        # Score basé sur déséquilibres catégoriels
        if categorie == 'C':
            # Catégorie C → BANKER favorisé (+12.1%)
            score = 20.0
            if index1_predit == 1:  # 1_C_* encore plus fort
                score += 5.0
            return 'BANKER', score
            
        elif categorie == 'B':
            # Catégorie B → PLAYER favorisé (+7.9%)
            score = 15.0
            if index1_predit == 1:  # 1_B_* renforcé
                score += 3.0
            return 'PLAYER', score
            
        else:  # Catégorie A - équilibre
            # Utiliser INDEX1 comme tie-breaker
            if index1_predit == 0:
                return 'BANKER', 1.0
            else:
                return 'PLAYER', 1.0

    def charger_dataset(self) -> Dict:
        """Chargement optimisé"""
        print("🔄 Chargement dataset...")
        
        taille_gb = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille: {taille_gb:.2f} GB")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        print(f"✅ {dataset['metadata']['nombre_parties']:,} parties chargées")
        return dataset

    def extraire_donnees_rapide(self, dataset: Dict) -> pd.DataFrame:
        """Extraction ultra-rapide avec multiprocessing"""
        print("🔍 Extraction données (multiprocessing)...")
        
        parties = dataset['parties']
        chunk_size = max(1000, len(parties) // MAX_WORKERS)
        chunks = [parties[i:i + chunk_size] for i in range(0, len(parties), chunk_size)]
        
        print(f"📦 {len(chunks)} chunks pour {MAX_WORKERS} workers")
        
        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [executor.submit(self._extraire_chunk_rapide, chunk) for chunk in chunks]
            
            donnees_totales = []
            for future in as_completed(futures):
                chunk_donnees = future.result()
                donnees_totales.extend(chunk_donnees)
        
        df = pd.DataFrame(donnees_totales)
        print(f"✅ {len(df):,} observations extraites")
        return df

    def _extraire_chunk_rapide(self, parties_chunk: List) -> List[Dict]:
        """Worker pour extraction rapide"""
        donnees = []
        
        for partie in parties_chunk:
            mains_valides = [m for m in partie['mains'] if m['main_number'] is not None]
            
            for i in range(1, len(mains_valides)):  # Commencer à 1
                main_precedente = mains_valides[i-1]
                main_actuelle = mains_valides[i]
                
                # Exclure TIE du target
                if main_actuelle['index3_result'] in ['BANKER', 'PLAYER']:
                    donnees.append({
                        'partie_id': partie['partie_number'],
                        'main_n': main_precedente['main_number'],
                        'sync_state_n': main_precedente['index1_sync_state'],
                        'categorie_n': main_precedente['index2_cards_category'],
                        'target': 1 if main_actuelle['index3_result'] == 'BANKER' else 0
                    })
        
        return donnees

    def executer_predictions_rapide(self, df: pd.DataFrame) -> pd.DataFrame:
        """🚀 PRÉDICTIONS VECTORISÉES ULTRA-RAPIDES"""
        print("🎯 Calcul prédictions vectorisées...")
        
        # Vectorisation pure - pas de multiprocessing pour éviter overhead
        predictions = []
        scores = []
        
        total = len(df)
        batch_size = 100000
        
        for i in range(0, total, batch_size):
            batch = df.iloc[i:i+batch_size]
            
            batch_pred = []
            batch_scores = []
            
            # Vectorisation par catégorie
            for _, row in batch.iterrows():
                pred, score = self.calculer_prediction_rapide(
                    row['sync_state_n'], 
                    row['categorie_n']
                )
                batch_pred.append(pred)
                batch_scores.append(score)
            
            predictions.extend(batch_pred)
            scores.extend(batch_scores)
            
            if (i // batch_size + 1) % 10 == 0:
                print(f"   📊 {i+len(batch):,}/{total:,} prédictions...")
        
        df['prediction'] = predictions
        df['score'] = scores
        df['prediction_numeric'] = df['prediction'].map({'BANKER': 1, 'PLAYER': 0})
        
        print(f"✅ {len(predictions):,} prédictions calculées")
        return df

    def evaluer_performance(self, df: pd.DataFrame) -> Dict:
        """Évaluation performance"""
        print("📈 Évaluation performance...")
        
        # Accuracy globale
        accuracy = (df['prediction_numeric'] == df['target']).mean()
        
        # Par catégorie
        stats_cat = {}
        for cat in ['A', 'B', 'C']:
            mask = df['categorie_n'] == cat
            if mask.sum() > 0:
                acc_cat = (df[mask]['prediction_numeric'] == df[mask]['target']).mean()
                stats_cat[cat] = {
                    'count': mask.sum(),
                    'accuracy': acc_cat,
                    'predictions': df[mask]['prediction'].value_counts().to_dict()
                }
        
        # Par prédiction
        stats_pred = {}
        for pred in ['BANKER', 'PLAYER']:
            mask = df['prediction'] == pred
            if mask.sum() > 0:
                acc_pred = (df[mask]['prediction_numeric'] == df[mask]['target']).mean()
                stats_pred[pred] = {
                    'count': mask.sum(),
                    'accuracy': acc_pred
                }
        
        resultats = {
            'accuracy_globale': accuracy,
            'total_predictions': len(df),
            'par_categorie': stats_cat,
            'par_prediction': stats_pred
        }
        
        return resultats

    def generer_rapport(self, resultats: Dict, temps_execution: float):
        """Génération rapport final"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_rapport = f"rapport_predicteur_index5_rapide_{timestamp}.txt"
        
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write("🚀 RAPPORT PRÉDICTEUR INDEX5 ULTRA-RAPIDE\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"⏱️  TEMPS EXÉCUTION: {temps_execution:.1f} secondes\n")
            f.write(f"📊 TOTAL PRÉDICTIONS: {resultats['total_predictions']:,}\n")
            f.write(f"🎯 ACCURACY GLOBALE: {resultats['accuracy_globale']:.3f} ({resultats['accuracy_globale']*100:.1f}%)\n\n")
            
            f.write("📈 PERFORMANCE PAR CATÉGORIE:\n")
            f.write("-" * 30 + "\n")
            for cat, stats in resultats['par_categorie'].items():
                f.write(f"Catégorie {cat}: {stats['accuracy']:.3f} ({stats['accuracy']*100:.1f}%) - {stats['count']:,} obs\n")
                for pred, count in stats['predictions'].items():
                    f.write(f"  → {pred}: {count:,}\n")
            
            f.write(f"\n🎯 PERFORMANCE PAR PRÉDICTION:\n")
            f.write("-" * 30 + "\n")
            for pred, stats in resultats['par_prediction'].items():
                f.write(f"{pred}: {stats['accuracy']:.3f} ({stats['accuracy']*100:.1f}%) - {stats['count']:,} prédictions\n")
        
        print(f"📄 Rapport sauvegardé: {nom_rapport}")
        return nom_rapport

def main():
    """Fonction principale ultra-optimisée"""
    debut = time.time()
    
    print("🚀 DÉMARRAGE PRÉDICTEUR INDEX5 ULTRA-RAPIDE")
    print("=" * 50)
    
    # Initialisation
    predicteur = PredicteurIndex5Rapide("dataset_baccarat_lupasco_20250627_090239.json")
    
    # Chargement
    dataset = predicteur.charger_dataset()
    
    # Extraction
    df_donnees = predicteur.extraire_donnees_rapide(dataset)
    
    # Prédictions
    df_predictions = predicteur.executer_predictions_rapide(df_donnees)
    
    # Évaluation
    resultats = predicteur.evaluer_performance(df_predictions)
    
    # Temps total
    temps_total = time.time() - debut
    
    # Rapport
    rapport = predicteur.generer_rapport(resultats, temps_total)
    
    print(f"\n🎉 TERMINÉ EN {temps_total:.1f} SECONDES!")
    print(f"🎯 Accuracy: {resultats['accuracy_globale']*100:.1f}%")
    print(f"📄 Rapport: {rapport}")

if __name__ == "__main__":
    main()
