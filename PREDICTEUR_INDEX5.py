#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PREDICTEUR_INDEX5.py - PRÉDICTEUR BANKER/PLAYER v1.0
Basé sur l'analyse INDEX5 - 2025-06-29

🎯 PRÉDICTEUR BANKER/PLAYER ULTRA-PERFORMANT
Architecture: Stratégie multi-niveaux basée sur déséquilibres INDEX5
Prédiction: Main n → BANKER/PLAYER à main n+1
Optimisé: 8 cœurs + 28GB RAM

STRATÉGIE BASÉE SUR RAPPORT INDEX5:
- Catégorie B (6 cartes): PLAYER favorisé (+7.9%)
- Catégorie C (5 cartes): BANKER favorisé (+12.1%)
- Équilibre SYNC/DESYNC: Retour automatique vers 50/50
"""

import json
import os
import sys
import time
import datetime
import traceback
import hashlib
from pathlib import Path
from collections import defaultdict, Counter
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any

# Optimisations performance
import warnings
warnings.filterwarnings('ignore')

# Configuration multiprocessing
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Configuration pandas pour performance
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

print("🎯 PRÉDICTEUR INDEX5 BANKER/PLAYER")
print("=" * 50)

class PredicteurIndex5:
    """
    Prédicteur BANKER/PLAYER basé sur l'analyse INDEX5
    Utilise les déséquilibres découverts dans le rapport
    """
    
    def __init__(self):
        self.dataset_path = "dataset_baccarat_lupasco_20250627_090239.json"
        self.strategies = {
            'categorie_cartes': True,
            'equilibre_sync_desync': True,
            'tendance_recente': True,
            'sous_representation': True
        }
        
        # Probabilités basées sur rapport INDEX5
        self.proba_categorie = {
            'A': {'BANKER': 45.3, 'PLAYER': 45.3, 'TIE': 9.4},  # Équilibre
            'B': {'BANKER': 40.9, 'PLAYER': 48.8, 'TIE': 10.3}, # PLAYER +7.9%
            'C': {'BANKER': 51.6, 'PLAYER': 39.5, 'TIE': 8.8}   # BANKER +12.1%
        }
        
        # Seuils de déséquilibre SYNC/DESYNC
        self.seuil_sync_desync = 0.55  # Au-delà de 55%, prédire retour équilibre
        
        print(f"📂 Dataset: {self.dataset_path}")
        print(f"🎲 Stratégies activées: {sum(self.strategies.values())}/4")

    def charger_dataset(self) -> Dict:
        """
        Chargement optimisé du dataset JSON
        """
        print("🔄 Chargement dataset...")
        
        if not os.path.exists(self.dataset_path):
            raise FileNotFoundError(f"Dataset non trouvé: {self.dataset_path}")
        
        taille_fichier = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille dataset: {taille_fichier:.2f} GB")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        print(f"✅ Dataset chargé: {dataset['metadata']['nombre_parties']:,} parties")
        return dataset

    def extraire_donnees_predictives(self, dataset: Dict) -> pd.DataFrame:
        """
        Extraction des données pour prédiction BANKER/PLAYER
        """
        print("🔍 Extraction données prédictives...")
        
        donnees = []
        parties_traitees = 0
        
        for partie in dataset['parties']:
            partie_id = partie['partie_number']
            mains_valides = [m for m in partie['mains'] if m['main_number'] is not None]
            
            for i, main in enumerate(mains_valides):
                # Ignorer la première main (pas de prédiction possible)
                if i == 0:
                    continue
                
                main_precedente = mains_valides[i-1]
                
                # Données de la main précédente (pour prédiction)
                donnee = {
                    'partie_id': partie_id,
                    'main_n': main_precedente['main_number'],
                    'main_n_plus_1': main['main_number'],
                    
                    # Caractéristiques main n (pour prédiction)
                    'sync_state_n': main_precedente['index1_sync_state'],
                    'cards_count_n': main_precedente['index2_cards_count'],
                    'cards_category_n': main_precedente['index2_cards_category'],
                    'result_n': main_precedente['index3_result'],
                    'index5_n': main_precedente['index5_combined'],
                    
                    # Résultat à prédire (main n+1)
                    'result_n_plus_1': main['index3_result'],
                    'target_banker_player': 1 if main['index3_result'] == 'BANKER' else 0  # 1=BANKER, 0=PLAYER
                }
                
                # Exclure les TIE du target (on ne prédit que BANKER/PLAYER)
                if main['index3_result'] in ['BANKER', 'PLAYER']:
                    donnees.append(donnee)
            
            parties_traitees += 1
            if parties_traitees % 10000 == 0:
                print(f"   📊 {parties_traitees:,} parties traitées...")
        
        df = pd.DataFrame(donnees)
        print(f"✅ {len(df):,} observations prédictives extraites")
        print(f"📈 Distribution: BANKER={len(df[df['target_banker_player']==1]):,}, PLAYER={len(df[df['target_banker_player']==0]):,}")
        
        return df

    def calculer_historique_sync_desync(self, df: pd.DataFrame, fenetre: int = 20) -> pd.DataFrame:
        """
        Calcule l'historique SYNC/DESYNC pour détecter déséquilibres
        """
        print(f"📊 Calcul historique SYNC/DESYNC (fenêtre {fenetre})...")
        
        df_enrichi = df.copy()
        df_enrichi['ratio_sync_recent'] = 0.0
        df_enrichi['deviation_sync'] = 0.0
        
        for partie_id in df['partie_id'].unique():
            mask_partie = df['partie_id'] == partie_id
            indices_partie = df[mask_partie].index.tolist()
            
            for i, idx in enumerate(indices_partie):
                if i < fenetre:
                    continue
                
                # Prendre les N dernières mains de cette partie
                indices_fenetre = indices_partie[max(0, i-fenetre):i]
                sync_states = df.loc[indices_fenetre, 'sync_state_n'].values
                
                ratio_sync = np.mean(sync_states == 0)  # 0 = SYNC
                deviation = abs(ratio_sync - 0.497)  # Écart par rapport à équilibre théorique
                
                df_enrichi.loc[idx, 'ratio_sync_recent'] = ratio_sync
                df_enrichi.loc[idx, 'deviation_sync'] = deviation
        
        return df_enrichi

    def predire_strategie_categorie(self, cards_category: str) -> str:
        """
        Prédiction basée sur la catégorie de cartes
        """
        if cards_category == 'A':
            return 'NEUTRE'  # Équilibre parfait
        elif cards_category == 'B':
            return 'PLAYER'  # +7.9% avantage
        elif cards_category == 'C':
            return 'BANKER'  # +12.1% avantage
        else:
            return 'NEUTRE'

    def predire_strategie_sync_desync(self, ratio_sync: float, deviation: float) -> str:
        """
        Prédiction basée sur déséquilibre SYNC/DESYNC
        """
        if ratio_sync > self.seuil_sync_desync:
            return 'DESYNC_ATTENDU'  # Trop de SYNC → prédire DESYNC
        elif ratio_sync < (1 - self.seuil_sync_desync):
            return 'SYNC_ATTENDU'   # Trop de DESYNC → prédire SYNC
        else:
            return 'EQUILIBRE'

    def analyser_tendance_recente(self, df: pd.DataFrame, partie_id: int, main_actuelle: int, fenetre: int = 10) -> Dict:
        """
        Analyse la tendance récente dans la partie
        """
        mask_partie = (df['partie_id'] == partie_id) & (df['main_n'] < main_actuelle)
        mains_recentes = df[mask_partie].tail(fenetre)

        if len(mains_recentes) == 0:
            return {'tendance': 'NEUTRE', 'force': 0.0}

        # Compter BANKER vs PLAYER dans les résultats récents
        banker_count = (mains_recentes['result_n'] == 'BANKER').sum()
        player_count = (mains_recentes['result_n'] == 'PLAYER').sum()
        total_bp = banker_count + player_count

        if total_bp == 0:
            return {'tendance': 'NEUTRE', 'force': 0.0}

        ratio_banker = banker_count / total_bp

        if ratio_banker > 0.65:
            return {'tendance': 'BANKER_DOMINANT', 'force': ratio_banker - 0.5}
        elif ratio_banker < 0.35:
            return {'tendance': 'PLAYER_DOMINANT', 'force': 0.5 - ratio_banker}
        else:
            return {'tendance': 'EQUILIBRE', 'force': abs(ratio_banker - 0.5)}

    def calculer_score_prediction(self, row: pd.Series, df_complet: pd.DataFrame) -> Tuple[str, float]:
        """
        Calcule le score de prédiction combiné avec toutes les stratégies
        """
        score_banker = 0.0
        score_player = 0.0

        # STRATÉGIE 1: Catégorie de cartes (PRIORITÉ MAXIMALE)
        if self.strategies['categorie_cartes']:
            pred_cat = self.predire_strategie_categorie(row['cards_category_n'])
            if pred_cat == 'BANKER':
                score_banker += 12.1  # Avantage Catégorie C
            elif pred_cat == 'PLAYER':
                score_player += 7.9   # Avantage Catégorie B

        # STRATÉGIE 2: Équilibre SYNC/DESYNC
        if self.strategies['equilibre_sync_desync'] and row['deviation_sync'] > 0.05:
            pred_sync = self.predire_strategie_sync_desync(row['ratio_sync_recent'], row['deviation_sync'])
            if pred_sync == 'SYNC_ATTENDU':
                # Favoriser selon les patterns historiques SYNC
                score_banker += 2.5
            elif pred_sync == 'DESYNC_ATTENDU':
                score_player += 2.5

        # STRATÉGIE 3: Tendance récente (contrarian)
        if self.strategies['tendance_recente']:
            tendance = self.analyser_tendance_recente(df_complet, row['partie_id'], row['main_n'])
            if tendance['tendance'] == 'BANKER_DOMINANT' and tendance['force'] > 0.15:
                # Contrarian: parier contre la tendance dominante
                score_player += 3.0 * tendance['force']
            elif tendance['tendance'] == 'PLAYER_DOMINANT' and tendance['force'] > 0.15:
                score_banker += 3.0 * tendance['force']

        # STRATÉGIE 4: Sous-représentation INDEX5
        if self.strategies['sous_representation']:
            # Patterns sous-représentés selon rapport INDEX5
            patterns_sous_repr = ['0_C_PLAYER', '1_C_PLAYER', '0_C_TIE', '1_C_TIE']
            if row['index5_n'] in patterns_sous_repr:
                if 'PLAYER' in row['index5_n']:
                    score_banker += 4.0  # Parier contre sous-représentation
                elif 'TIE' in row['index5_n']:
                    # Après TIE, légère préférence BANKER selon stats
                    score_banker += 1.5

        # STRATÉGIE 5: Patterns sur-représentés
        patterns_sur_repr = ['1_A_PLAYER', '1_A_BANKER', '0_A_PLAYER', '0_A_BANKER']
        if row['index5_n'] in patterns_sur_repr:
            # Continuer la tendance des patterns fréquents
            if 'BANKER' in row['index5_n']:
                score_banker += 1.0
            elif 'PLAYER' in row['index5_n']:
                score_player += 1.0

        # Décision finale avec logique sophistiquée
        diff_score = abs(score_banker - score_player)

        if score_banker > score_player:
            return 'BANKER', diff_score
        elif score_player > score_banker:
            return 'PLAYER', diff_score
        else:
            # En cas d'égalité parfaite, utiliser hiérarchie des catégories
            if row['cards_category_n'] == 'C':
                return 'BANKER', 1.0  # Catégorie C favorise BANKER
            elif row['cards_category_n'] == 'B':
                return 'PLAYER', 1.0  # Catégorie B favorise PLAYER
            else:
                # Catégorie A: utiliser SYNC/DESYNC comme tie-breaker
                if row['sync_state_n'] == 0:  # SYNC
                    return 'BANKER', 0.5
                else:  # DESYNC
                    return 'PLAYER', 0.5

    def executer_predictions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Exécute les prédictions sur tout le dataset avec optimisation multiprocessing
        """
        print("🎯 Exécution prédictions...")

        # Enrichir avec historique SYNC/DESYNC
        df_enrichi = self.calculer_historique_sync_desync(df)

        # Calculer prédictions avec optimisation vectorielle
        print("🔄 Calcul prédictions vectorisées...")
        predictions = []
        scores = []
        strategies_utilisees = []

        total_rows = len(df_enrichi)
        batch_size = 10000

        for i in range(0, total_rows, batch_size):
            batch_end = min(i + batch_size, total_rows)
            batch = df_enrichi.iloc[i:batch_end]

            batch_predictions = []
            batch_scores = []
            batch_strategies = []

            for idx, row in batch.iterrows():
                pred, score = self.calculer_score_prediction(row, df_enrichi)

                # Identifier stratégie principale utilisée
                strategie_principale = self.identifier_strategie_principale(row, score)

                batch_predictions.append(pred)
                batch_scores.append(score)
                batch_strategies.append(strategie_principale)

            predictions.extend(batch_predictions)
            scores.extend(batch_scores)
            strategies_utilisees.extend(batch_strategies)

            if (i // batch_size + 1) % 10 == 0:
                print(f"   📊 {batch_end:,}/{total_rows:,} prédictions calculées...")

        df_enrichi['prediction'] = predictions
        df_enrichi['score_confiance'] = scores
        df_enrichi['strategie_principale'] = strategies_utilisees

        # Convertir prédictions en format numérique pour évaluation
        df_enrichi['prediction_numeric'] = df_enrichi['prediction'].map({'BANKER': 1, 'PLAYER': 0})

        print(f"✅ {len(predictions):,} prédictions calculées")
        return df_enrichi

    def identifier_strategie_principale(self, row: pd.Series, score_final: float) -> str:
        """
        Identifie quelle stratégie a été la plus influente
        """
        if row['cards_category_n'] == 'C':
            return 'CATEGORIE_C_BANKER'
        elif row['cards_category_n'] == 'B':
            return 'CATEGORIE_B_PLAYER'
        elif row['deviation_sync'] > 0.1:
            return 'EQUILIBRE_SYNC_DESYNC'
        elif row['index5_n'] in ['0_C_PLAYER', '1_C_PLAYER']:
            return 'SOUS_REPRESENTATION'
        else:
            return 'TENDANCE_GENERALE'

    def evaluer_performance(self, df_predictions: pd.DataFrame) -> Dict:
        """
        Évalue la performance du prédicteur avec analyse détaillée
        """
        print("📊 Évaluation performance avancée...")

        # Filtrer les observations avec target valide (BANKER/PLAYER seulement)
        df_eval = df_predictions.dropna(subset=['target_banker_player', 'prediction_numeric'])

        # Calculs de performance globale
        total_predictions = len(df_eval)
        predictions_correctes = (df_eval['prediction_numeric'] == df_eval['target_banker_player']).sum()
        accuracy = (predictions_correctes / total_predictions) * 100

        # Performance par catégorie de cartes
        perf_par_categorie = {}
        for cat in ['A', 'B', 'C']:
            mask_cat = df_eval['cards_category_n'] == cat
            if mask_cat.sum() > 0:
                correct_cat = (df_eval[mask_cat]['prediction_numeric'] == df_eval[mask_cat]['target_banker_player']).sum()
                total_cat = mask_cat.sum()
                perf_par_categorie[cat] = {
                    'accuracy': (correct_cat / total_cat) * 100,
                    'total': total_cat,
                    'correct': correct_cat
                }

        # Performance par type de prédiction
        perf_banker = df_eval[df_eval['prediction'] == 'BANKER']
        perf_player = df_eval[df_eval['prediction'] == 'PLAYER']

        accuracy_banker = ((perf_banker['prediction_numeric'] == perf_banker['target_banker_player']).sum() / len(perf_banker) * 100) if len(perf_banker) > 0 else 0
        accuracy_player = ((perf_player['prediction_numeric'] == perf_player['target_banker_player']).sum() / len(perf_player) * 100) if len(perf_player) > 0 else 0

        # Performance par stratégie principale
        perf_par_strategie = {}
        for strategie in df_eval['strategie_principale'].unique():
            mask_strat = df_eval['strategie_principale'] == strategie
            if mask_strat.sum() > 0:
                correct_strat = (df_eval[mask_strat]['prediction_numeric'] == df_eval[mask_strat]['target_banker_player']).sum()
                total_strat = mask_strat.sum()
                perf_par_strategie[strategie] = {
                    'accuracy': (correct_strat / total_strat) * 100,
                    'total': total_strat,
                    'correct': correct_strat
                }

        # Analyse par niveau de confiance
        df_eval['confiance_niveau'] = pd.cut(df_eval['score_confiance'],
                                           bins=[0, 2, 5, 10, float('inf')],
                                           labels=['Faible', 'Moyen', 'Élevé', 'Très_Élevé'])

        perf_par_confiance = {}
        for niveau in df_eval['confiance_niveau'].unique():
            if pd.isna(niveau):
                continue
            mask_conf = df_eval['confiance_niveau'] == niveau
            if mask_conf.sum() > 0:
                correct_conf = (df_eval[mask_conf]['prediction_numeric'] == df_eval[mask_conf]['target_banker_player']).sum()
                total_conf = mask_conf.sum()
                perf_par_confiance[str(niveau)] = {
                    'accuracy': (correct_conf / total_conf) * 100,
                    'total': total_conf,
                    'correct': correct_conf
                }

        # Calcul de l'avantage par rapport au hasard (50%)
        avantage_hasard = accuracy - 50.0

        resultats = {
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'accuracy_globale': accuracy,
            'avantage_hasard': avantage_hasard,
            'accuracy_banker': accuracy_banker,
            'accuracy_player': accuracy_player,
            'performance_par_categorie': perf_par_categorie,
            'performance_par_strategie': perf_par_strategie,
            'performance_par_confiance': perf_par_confiance,
            'distribution_predictions': {
                'BANKER': len(perf_banker),
                'PLAYER': len(perf_player)
            },
            'score_confiance_moyen': df_eval['score_confiance'].mean(),
            'score_confiance_median': df_eval['score_confiance'].median()
        }

        return resultats

    def sauvegarder_rapport_performance(self, resultats: Dict) -> str:
        """
        Sauvegarde un rapport détaillé de performance
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_fichier = f"rapport_predicteur_index5_{timestamp}.txt"

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write("RAPPORT PERFORMANCE PRÉDICTEUR INDEX5\n")
            f.write("=" * 50 + "\n")
            f.write(f"Généré le : {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Performance globale
            f.write("PERFORMANCE GLOBALE\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total prédictions : {resultats['total_predictions']:,}\n")
            f.write(f"Prédictions correctes : {resultats['predictions_correctes']:,}\n")
            f.write(f"Accuracy globale : {resultats['accuracy_globale']:.2f}%\n")
            f.write(f"Avantage vs hasard : {resultats['avantage_hasard']:+.2f}%\n")
            f.write(f"Accuracy BANKER : {resultats['accuracy_banker']:.2f}%\n")
            f.write(f"Accuracy PLAYER : {resultats['accuracy_player']:.2f}%\n\n")

            # Performance par catégorie
            f.write("PERFORMANCE PAR CATÉGORIE DE CARTES\n")
            f.write("-" * 40 + "\n")
            for cat, perf in resultats['performance_par_categorie'].items():
                f.write(f"Catégorie {cat} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Performance par stratégie
            f.write("PERFORMANCE PAR STRATÉGIE\n")
            f.write("-" * 30 + "\n")
            for strat, perf in resultats['performance_par_strategie'].items():
                f.write(f"{strat} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Performance par niveau de confiance
            f.write("PERFORMANCE PAR NIVEAU DE CONFIANCE\n")
            f.write("-" * 40 + "\n")
            for niveau, perf in resultats['performance_par_confiance'].items():
                f.write(f"{niveau} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Distribution
            f.write("DISTRIBUTION DES PRÉDICTIONS\n")
            f.write("-" * 35 + "\n")
            f.write(f"BANKER : {resultats['distribution_predictions']['BANKER']:,}\n")
            f.write(f"PLAYER : {resultats['distribution_predictions']['PLAYER']:,}\n")
            f.write(f"Score confiance moyen : {resultats['score_confiance_moyen']:.2f}\n")
            f.write(f"Score confiance médian : {resultats['score_confiance_median']:.2f}\n")

        print(f"📄 Rapport sauvegardé : {nom_fichier}")
        return nom_fichier

def main():
    """
    Fonction principale du prédicteur
    """
    try:
        print("🚀 DÉMARRAGE PRÉDICTEUR INDEX5")
        print("=" * 50)
        
        # Initialisation
        predicteur = PredicteurIndex5()
        
        # Chargement dataset
        dataset = predicteur.charger_dataset()
        
        # Extraction données
        df_donnees = predicteur.extraire_donnees_predictives(dataset)
        
        # Exécution prédictions
        df_predictions = predicteur.executer_predictions(df_donnees)
        
        # Évaluation
        resultats = predicteur.evaluer_performance(df_predictions)

        # Sauvegarde rapport
        nom_rapport = predicteur.sauvegarder_rapport_performance(resultats)

        # Affichage résultats
        print("\n🎯 RÉSULTATS PRÉDICTEUR INDEX5:")
        print("=" * 50)
        print(f"Total prédictions: {resultats['total_predictions']:,}")
        print(f"Prédictions correctes: {resultats['predictions_correctes']:,}")
        print(f"Accuracy globale: {resultats['accuracy_globale']:.2f}%")
        print(f"Avantage vs hasard: {resultats['avantage_hasard']:+.2f}%")
        print(f"Accuracy BANKER: {resultats['accuracy_banker']:.2f}%")
        print(f"Accuracy PLAYER: {resultats['accuracy_player']:.2f}%")

        print(f"\n📊 Performance par catégorie:")
        for cat, perf in resultats['performance_par_categorie'].items():
            print(f"  Catégorie {cat}: {perf['accuracy']:.2f}% ({perf['total']:,} obs)")

        print(f"\n🎯 Performance par stratégie:")
        for strat, perf in sorted(resultats['performance_par_strategie'].items(),
                                key=lambda x: x[1]['accuracy'], reverse=True):
            print(f"  {strat}: {perf['accuracy']:.2f}% ({perf['total']:,} obs)")

        print(f"\n📈 Distribution prédictions:")
        print(f"  BANKER: {resultats['distribution_predictions']['BANKER']:,}")
        print(f"  PLAYER: {resultats['distribution_predictions']['PLAYER']:,}")

        print(f"\n📊 Scores de confiance:")
        print(f"  Moyen: {resultats['score_confiance_moyen']:.2f}")
        print(f"  Médian: {resultats['score_confiance_median']:.2f}")

        print(f"\n📄 Rapport détaillé: {nom_rapport}")
        print(f"\n✅ PRÉDICTEUR INDEX5 TERMINÉ AVEC SUCCÈS")

        # Analyse de la performance
        if resultats['accuracy_globale'] > 52.0:
            print(f"🎉 EXCELLENT: Accuracy > 52% - Avantage significatif!")
        elif resultats['accuracy_globale'] > 50.5:
            print(f"✅ BON: Accuracy > 50.5% - Avantage détectable")
        else:
            print(f"⚠️  ATTENTION: Accuracy ≤ 50.5% - Revoir stratégies")

        return True
        
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
