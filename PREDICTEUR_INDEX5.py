#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PREDICTEUR_INDEX5.py - PRÉDICTEUR BANKER/PLAYER v1.0
Basé sur l'analyse INDEX5 - 2025-06-29

🎯 PRÉDICTEUR BANKER/PLAYER ULTRA-PERFORMANT
Architecture: Stratégie multi-niveaux basée sur déséquilibres INDEX5
Prédiction: Main n → BANKER/PLAYER à main n+1
Optimisé: 8 cœurs + 28GB RAM

STRATÉGIE BASÉE SUR RAPPORT INDEX5:
- Catégorie B (6 cartes): PLAYER favorisé (+7.9%)
- Catégorie C (5 cartes): BANKER favorisé (+12.1%)
- Équilibre SYNC/DESYNC: Retour automatique vers 50/50
"""

import json
import os
import sys
import time
import datetime
import traceback
import hashlib
from pathlib import Path
from collections import defaultdict, Counter
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any

# Optimisations performance
import warnings
warnings.filterwarnings('ignore')

# Configuration multiprocessing optimisée
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import partial

# Configuration pandas pour performance
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

# Configuration optimisation mémoire et CPU
CPU_COUNT = mp.cpu_count()
CHUNK_SIZE = 50000  # Optimisé pour 28GB RAM
MAX_WORKERS = min(CPU_COUNT, 8)  # Utiliser tous les 8 cœurs

print(f"🚀 Configuration système: {CPU_COUNT} cœurs détectés, {MAX_WORKERS} workers utilisés")

print("🎯 PRÉDICTEUR INDEX5 BANKER/PLAYER")
print("=" * 50)

class PredicteurIndex5:
    """
    Prédicteur BANKER/PLAYER basé sur l'analyse INDEX5
    Utilise les déséquilibres découverts dans le rapport
    """
    
    def __init__(self):
        self.dataset_path = "dataset_baccarat_lupasco_20250627_090239.json"
        self.strategies = {
            'categorie_cartes': True,
            'equilibre_sync_desync': True,
            'tendance_recente': True,
            'sous_representation': True
        }
        
        # Probabilités basées sur rapport INDEX5
        self.proba_categorie = {
            'A': {'BANKER': 45.3, 'PLAYER': 45.3, 'TIE': 9.4},  # Équilibre
            'B': {'BANKER': 40.9, 'PLAYER': 48.8, 'TIE': 10.3}, # PLAYER +7.9%
            'C': {'BANKER': 51.6, 'PLAYER': 39.5, 'TIE': 8.8}   # BANKER +12.1%
        }

        # 🚨 RÈGLES DE TRANSITION INDEX1 DÉTERMINISTES
        self.regles_transition_index1 = {
            'C': 'INVERSION',    # C inverse toujours : 0→1, 1→0
            'A': 'CONSERVATION', # A conserve toujours : 0→0, 1→1
            'B': 'CONSERVATION'  # B conserve toujours : 0→0, 1→1
        }
        
        # Seuils de déséquilibre SYNC/DESYNC
        self.seuil_sync_desync = 0.55  # Au-delà de 55%, prédire retour équilibre
        
        print(f"📂 Dataset: {self.dataset_path}")
        print(f"🎲 Stratégies activées: {sum(self.strategies.values())}/4")
        print(f"🚨 Règles INDEX1: C={self.regles_transition_index1['C']}, A={self.regles_transition_index1['A']}, B={self.regles_transition_index1['B']}")



    def charger_dataset(self) -> Dict:
        """
        Chargement optimisé du dataset JSON avec gestion mémoire
        """
        print("🔄 Chargement dataset optimisé...")

        if not os.path.exists(self.dataset_path):
            raise FileNotFoundError(f"Dataset non trouvé: {self.dataset_path}")

        taille_fichier = os.path.getsize(self.dataset_path) / (1024**3)
        print(f"📊 Taille dataset: {taille_fichier:.2f} GB")
        print(f"💾 RAM disponible: ~28GB, Chunk size: {CHUNK_SIZE:,}")

        # Chargement avec optimisation mémoire
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)

        print(f"✅ Dataset chargé: {dataset['metadata']['nombre_parties']:,} parties")
        print(f"🔧 Workers configurés: {MAX_WORKERS} processus parallèles")
        return dataset

    def extraire_donnees_predictives(self, dataset: Dict) -> pd.DataFrame:
        """
        Extraction optimisée des données avec multiprocessing
        """
        print("🔍 Extraction données prédictives (multiprocessing)...")

        # Diviser les parties en chunks pour multiprocessing
        parties = dataset['parties']
        total_parties = len(parties)
        chunk_size = max(1, total_parties // MAX_WORKERS)

        chunks = [parties[i:i + chunk_size] for i in range(0, total_parties, chunk_size)]
        print(f"📦 {len(chunks)} chunks créés pour {MAX_WORKERS} workers")

        # Traitement parallèle
        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = [executor.submit(self._extraire_chunk, chunk, i) for i, chunk in enumerate(chunks)]

            donnees_totales = []
            for future in as_completed(futures):
                chunk_donnees = future.result()
                donnees_totales.extend(chunk_donnees)
                print(f"   ✅ Chunk terminé: {len(chunk_donnees):,} observations")

        df = pd.DataFrame(donnees_totales)
        print(f"✅ {len(df):,} observations prédictives extraites")
        print(f"📈 Distribution: BANKER={len(df[df['target_banker_player']==1]):,}, PLAYER={len(df[df['target_banker_player']==0]):,}")

        return df

    def _extraire_chunk(self, parties_chunk: List, chunk_id: int) -> List[Dict]:
        """
        Extraction d'un chunk de parties (fonction worker)
        """
        donnees = []

        for partie in parties_chunk:
            partie_id = partie['partie_number']
            mains_valides = [m for m in partie['mains'] if m['main_number'] is not None]

            for i, main in enumerate(mains_valides):
                # Ignorer la première main (pas de prédiction possible)
                if i == 0:
                    continue

                main_precedente = mains_valides[i-1]

                # Données de la main précédente (pour prédiction)
                donnee = {
                    'partie_id': partie_id,
                    'main_n': main_precedente['main_number'],
                    'main_n_plus_1': main['main_number'],

                    # Caractéristiques main n (pour prédiction)
                    'sync_state_n': main_precedente['index1_sync_state'],
                    'cards_count_n': main_precedente['index2_cards_count'],
                    'cards_category_n': main_precedente['index2_cards_category'],
                    'result_n': main_precedente['index3_result'],
                    'index5_n': main_precedente['index5_combined'],

                    # Résultat à prédire (main n+1)
                    'result_n_plus_1': main['index3_result'],
                    'target_banker_player': 1 if main['index3_result'] == 'BANKER' else 0  # 1=BANKER, 0=PLAYER
                }

                # Exclure les TIE du target (on ne prédit que BANKER/PLAYER)
                if main['index3_result'] in ['BANKER', 'PLAYER']:
                    donnees.append(donnee)

        return donnees

    def calculer_historique_sync_desync(self, df: pd.DataFrame, fenetre: int = 20) -> pd.DataFrame:
        """
        🚀 CALCUL HISTORIQUE SYNC/DESYNC VECTORISÉ (OPTIMISÉ 8 CŒURS)
        """
        print(f"📊 Calcul historique SYNC/DESYNC vectorisé (fenêtre {fenetre})...")

        df_enrichi = df.copy()
        df_enrichi['ratio_sync_recent'] = 0.0
        df_enrichi['deviation_sync'] = 0.0

        # Traitement vectorisé par partie
        parties_uniques = df['partie_id'].unique()
        total_parties = len(parties_uniques)

        # Diviser les parties en chunks pour multiprocessing
        chunk_size = max(1, total_parties // MAX_WORKERS)
        parties_chunks = [parties_uniques[i:i + chunk_size] for i in range(0, total_parties, chunk_size)]

        print(f"🔧 Traitement {total_parties:,} parties en {len(parties_chunks)} chunks")

        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = []
            for chunk in parties_chunks:
                future = executor.submit(self._calculer_sync_desync_chunk, df, chunk, fenetre)
                futures.append(future)

            # Collecter les résultats
            for future in as_completed(futures):
                indices_maj, ratios, deviations = future.result()
                df_enrichi.loc[indices_maj, 'ratio_sync_recent'] = ratios
                df_enrichi.loc[indices_maj, 'deviation_sync'] = deviations

        print(f"✅ Historique SYNC/DESYNC calculé pour {len(df_enrichi):,} observations")
        return df_enrichi

    def _calculer_sync_desync_chunk(self, df: pd.DataFrame, parties_chunk: np.ndarray, fenetre: int) -> Tuple[List, List, List]:
        """
        Calcule SYNC/DESYNC pour un chunk de parties (fonction worker)
        """
        indices_maj = []
        ratios = []
        deviations = []

        for partie_id in parties_chunk:
            mask_partie = df['partie_id'] == partie_id
            indices_partie = df[mask_partie].index.tolist()
            sync_states_partie = df.loc[indices_partie, 'sync_state_n'].values

            for i, idx in enumerate(indices_partie):
                if i < fenetre:
                    continue

                # Vectorisation: prendre fenêtre glissante
                start_idx = max(0, i - fenetre)
                sync_window = sync_states_partie[start_idx:i]

                ratio_sync = np.mean(sync_window == 0)  # 0 = SYNC
                deviation = abs(ratio_sync - 0.497)  # Écart équilibre théorique

                indices_maj.append(idx)
                ratios.append(ratio_sync)
                deviations.append(deviation)

        return indices_maj, ratios, deviations

    def predire_strategie_categorie(self, cards_category: str) -> str:
        """
        Prédiction basée sur la catégorie de cartes
        """
        if cards_category == 'A':
            return 'NEUTRE'  # Équilibre parfait
        elif cards_category == 'B':
            return 'PLAYER'  # +7.9% avantage
        elif cards_category == 'C':
            return 'BANKER'  # +12.1% avantage
        else:
            return 'NEUTRE'

    def predire_index1_suivant(self, index1_actuel: int, cards_category: str) -> int:
        """
        🚨 PRÉDICTION DÉTERMINISTE INDEX1 SUIVANT
        Basée sur les règles de transition découvertes
        """
        if cards_category == 'C':
            # Catégorie C : INVERSION systématique
            return 1 - index1_actuel  # 0→1, 1→0
        elif cards_category in ['A', 'B']:
            # Catégories A et B : CONSERVATION
            return index1_actuel      # 0→0, 1→1
        else:
            # Fallback (ne devrait pas arriver)
            return index1_actuel

    def predire_strategie_sync_desync(self, ratio_sync: float, deviation: float) -> str:
        """
        Prédiction basée sur déséquilibre SYNC/DESYNC
        """
        if ratio_sync > self.seuil_sync_desync:
            return 'DESYNC_ATTENDU'  # Trop de SYNC → prédire DESYNC
        elif ratio_sync < (1 - self.seuil_sync_desync):
            return 'SYNC_ATTENDU'   # Trop de DESYNC → prédire SYNC
        else:
            return 'EQUILIBRE'

    def analyser_tendance_recente(self, df: pd.DataFrame, partie_id: int, main_actuelle: int, fenetre: int = 10) -> Dict:
        """
        Analyse la tendance récente dans la partie
        """
        mask_partie = (df['partie_id'] == partie_id) & (df['main_n'] < main_actuelle)
        mains_recentes = df[mask_partie].tail(fenetre)

        if len(mains_recentes) == 0:
            return {'tendance': 'NEUTRE', 'force': 0.0}

        # Compter BANKER vs PLAYER dans les résultats récents
        banker_count = (mains_recentes['result_n'] == 'BANKER').sum()
        player_count = (mains_recentes['result_n'] == 'PLAYER').sum()
        total_bp = banker_count + player_count

        if total_bp == 0:
            return {'tendance': 'NEUTRE', 'force': 0.0}

        ratio_banker = banker_count / total_bp

        if ratio_banker > 0.65:
            return {'tendance': 'BANKER_DOMINANT', 'force': ratio_banker - 0.5}
        elif ratio_banker < 0.35:
            return {'tendance': 'PLAYER_DOMINANT', 'force': 0.5 - ratio_banker}
        else:
            return {'tendance': 'EQUILIBRE', 'force': abs(ratio_banker - 0.5)}

    def calculer_score_prediction(self, row: pd.Series, df_complet: pd.DataFrame) -> Tuple[str, float]:
        """
        🎯 CALCUL SCORE PRÉDICTION AVEC RÈGLES DÉTERMINISTES INDEX1
        """
        score_banker = 0.0
        score_player = 0.0

        # 🚨 STRATÉGIE 0: PRÉDICTION DÉTERMINISTE INDEX1 (PRIORITÉ ABSOLUE)
        index1_predit = self.predire_index1_suivant(row['sync_state_n'], row['cards_category_n'])

        # Combiner INDEX1 prédit avec probabilités catégorie pour prédiction finale
        categorie = row['cards_category_n']

        # Probabilités conditionnelles basées sur INDEX1 prédit + catégorie
        if index1_predit == 0:  # SYNC prédit
            if categorie == 'C':
                # 0_C_* : BANKER favorisé (0_C_BANKER > 0_C_PLAYER selon rapport)
                score_banker += 15.0  # Boost majeur
            elif categorie == 'B':
                # 0_B_* : PLAYER légèrement favorisé
                score_player += 8.0
            elif categorie == 'A':
                # 0_A_* : Équilibre parfait
                score_banker += 0.5  # Léger biais
        else:  # DESYNC prédit (index1_predit == 1)
            if categorie == 'C':
                # 1_C_* : BANKER encore plus favorisé
                score_banker += 18.0  # Boost maximum
            elif categorie == 'B':
                # 1_B_* : PLAYER favorisé
                score_player += 10.0
            elif categorie == 'A':
                # 1_A_* : Équilibre avec léger biais PLAYER
                score_player += 0.5

        # STRATÉGIE 1: Catégorie de cartes (PRIORITÉ ÉLEVÉE)
        if self.strategies['categorie_cartes']:
            pred_cat = self.predire_strategie_categorie(row['cards_category_n'])
            if pred_cat == 'BANKER':
                score_banker += 5.0   # Réduit car INDEX1 est plus important
            elif pred_cat == 'PLAYER':
                score_player += 4.0   # Réduit car INDEX1 est plus important

        # STRATÉGIE 2: Validation croisée avec équilibre SYNC/DESYNC
        if self.strategies['equilibre_sync_desync'] and row['deviation_sync'] > 0.05:
            # Vérifier cohérence avec prédiction INDEX1
            if index1_predit == 0 and row['ratio_sync_recent'] < 0.45:
                # INDEX1 prédit SYNC mais historique montre manque de SYNC → cohérent
                score_banker += 1.0
            elif index1_predit == 1 and row['ratio_sync_recent'] > 0.55:
                # INDEX1 prédit DESYNC mais historique montre trop de SYNC → cohérent
                score_player += 1.0

        # STRATÉGIE 3: Tendance récente (réduite car INDEX1 est déterministe)
        if self.strategies['tendance_recente']:
            tendance = self.analyser_tendance_recente(df_complet, row['partie_id'], row['main_n'])
            if tendance['tendance'] == 'BANKER_DOMINANT' and tendance['force'] > 0.2:
                # Contrarian léger
                score_player += 1.5 * tendance['force']
            elif tendance['tendance'] == 'PLAYER_DOMINANT' and tendance['force'] > 0.2:
                score_banker += 1.5 * tendance['force']

        # STRATÉGIE 4: Patterns INDEX5 spécifiques (ajustée avec INDEX1)
        if self.strategies['sous_representation']:
            # Utiliser la prédiction INDEX1 pour affiner
            index5_predit_base = f"{index1_predit}_{categorie}_"

            # Patterns historiquement sous-représentés
            if index5_predit_base == "0_C_" or index5_predit_base == "1_C_":
                # Catégorie C avec prédiction INDEX1 → BANKER très favorisé
                score_banker += 2.0
            elif index5_predit_base == "0_B_" or index5_predit_base == "1_B_":
                # Catégorie B → PLAYER favorisé mais modéré
                score_player += 1.5

        # Décision finale basée sur INDEX1 déterministe + scores
        diff_score = abs(score_banker - score_player)

        if score_banker > score_player:
            return 'BANKER', diff_score
        elif score_player > score_banker:
            return 'PLAYER', diff_score
        else:
            # En cas d'égalité, utiliser la prédiction INDEX1 pure
            if index1_predit == 0:  # SYNC prédit
                if categorie == 'C':
                    return 'BANKER', 2.0  # 0_C_* favorise BANKER
                elif categorie == 'B':
                    return 'PLAYER', 1.0  # 0_B_* favorise PLAYER
                else:  # A
                    return 'BANKER', 0.5  # Léger biais
            else:  # DESYNC prédit
                if categorie == 'C':
                    return 'BANKER', 3.0  # 1_C_* favorise encore plus BANKER
                elif categorie == 'B':
                    return 'PLAYER', 1.5  # 1_B_* favorise PLAYER
                else:  # A
                    return 'PLAYER', 0.5  # Léger biais opposé

    def executer_predictions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        🚀 EXÉCUTION PRÉDICTIONS ULTRA-OPTIMISÉE (8 CŒURS + 28GB RAM)
        """
        print("🎯 Exécution prédictions multiprocessing...")

        # Enrichir avec historique SYNC/DESYNC
        print("📊 Calcul historique SYNC/DESYNC...")
        df_enrichi = self.calculer_historique_sync_desync(df)

        # Diviser en chunks optimisés pour la mémoire
        total_rows = len(df_enrichi)
        chunk_size = min(CHUNK_SIZE, max(1000, total_rows // MAX_WORKERS))

        chunks_indices = [(i, min(i + chunk_size, total_rows)) for i in range(0, total_rows, chunk_size)]
        print(f"🔧 {len(chunks_indices)} chunks de {chunk_size:,} lignes pour {MAX_WORKERS} workers")

        # Traitement parallèle des prédictions
        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Préparer les chunks de données
            chunk_futures = []
            for start_idx, end_idx in chunks_indices:
                chunk_df = df_enrichi.iloc[start_idx:end_idx].copy()
                future = executor.submit(self._calculer_predictions_chunk, chunk_df, df_enrichi)
                chunk_futures.append(future)

            # Collecter les résultats
            predictions_totales = []
            scores_totaux = []
            strategies_totales = []

            for i, future in enumerate(as_completed(chunk_futures)):
                chunk_predictions, chunk_scores, chunk_strategies = future.result()
                predictions_totales.extend(chunk_predictions)
                scores_totaux.extend(chunk_scores)
                strategies_totales.extend(chunk_strategies)

                print(f"   ✅ Chunk {i+1}/{len(chunk_futures)} terminé: {len(chunk_predictions):,} prédictions")

        # Assembler les résultats
        df_enrichi['prediction'] = predictions_totales
        df_enrichi['score_confiance'] = scores_totaux
        df_enrichi['strategie_principale'] = strategies_totales

        # Convertir prédictions en format numérique pour évaluation
        df_enrichi['prediction_numeric'] = df_enrichi['prediction'].map({'BANKER': 1, 'PLAYER': 0})

        print(f"✅ {len(predictions_totales):,} prédictions calculées avec {MAX_WORKERS} cœurs")
        return df_enrichi

    def _calculer_predictions_chunk(self, chunk_df: pd.DataFrame, df_complet: pd.DataFrame) -> Tuple[List, List, List]:
        """
        Calcule les prédictions pour un chunk (fonction worker)
        """
        predictions = []
        scores = []
        strategies = []

        for idx, row in chunk_df.iterrows():
            pred, score = self.calculer_score_prediction(row, df_complet)
            strategie = self.identifier_strategie_principale(row, score)

            predictions.append(pred)
            scores.append(score)
            strategies.append(strategie)

        return predictions, scores, strategies

    def identifier_strategie_principale(self, row: pd.Series, score_final: float) -> str:
        """
        Identifie quelle stratégie a été la plus influente
        """
        # Prédire INDEX1 pour identifier la stratégie
        index1_predit = self.predire_index1_suivant(row['sync_state_n'], row['cards_category_n'])
        categorie = row['cards_category_n']

        # La stratégie principale est toujours basée sur INDEX1 + catégorie
        if categorie == 'C':
            if index1_predit == 0:
                return 'INDEX1_DETERMINISTE_0_C_BANKER'
            else:
                return 'INDEX1_DETERMINISTE_1_C_BANKER'
        elif categorie == 'B':
            if index1_predit == 0:
                return 'INDEX1_DETERMINISTE_0_B_PLAYER'
            else:
                return 'INDEX1_DETERMINISTE_1_B_PLAYER'
        else:  # A
            if index1_predit == 0:
                return 'INDEX1_DETERMINISTE_0_A_EQUILIBRE'
            else:
                return 'INDEX1_DETERMINISTE_1_A_EQUILIBRE'

    def evaluer_performance(self, df_predictions: pd.DataFrame) -> Dict:
        """
        Évalue la performance du prédicteur avec analyse détaillée
        """
        print("📊 Évaluation performance avancée...")

        # Filtrer les observations avec target valide (BANKER/PLAYER seulement)
        df_eval = df_predictions.dropna(subset=['target_banker_player', 'prediction_numeric'])

        # Calculs de performance globale
        total_predictions = len(df_eval)
        predictions_correctes = (df_eval['prediction_numeric'] == df_eval['target_banker_player']).sum()
        accuracy = (predictions_correctes / total_predictions) * 100

        # Performance par catégorie de cartes
        perf_par_categorie = {}
        for cat in ['A', 'B', 'C']:
            mask_cat = df_eval['cards_category_n'] == cat
            if mask_cat.sum() > 0:
                correct_cat = (df_eval[mask_cat]['prediction_numeric'] == df_eval[mask_cat]['target_banker_player']).sum()
                total_cat = mask_cat.sum()
                perf_par_categorie[cat] = {
                    'accuracy': (correct_cat / total_cat) * 100,
                    'total': total_cat,
                    'correct': correct_cat
                }

        # Performance par type de prédiction
        perf_banker = df_eval[df_eval['prediction'] == 'BANKER']
        perf_player = df_eval[df_eval['prediction'] == 'PLAYER']

        accuracy_banker = ((perf_banker['prediction_numeric'] == perf_banker['target_banker_player']).sum() / len(perf_banker) * 100) if len(perf_banker) > 0 else 0
        accuracy_player = ((perf_player['prediction_numeric'] == perf_player['target_banker_player']).sum() / len(perf_player) * 100) if len(perf_player) > 0 else 0

        # Performance par stratégie principale
        perf_par_strategie = {}
        for strategie in df_eval['strategie_principale'].unique():
            mask_strat = df_eval['strategie_principale'] == strategie
            if mask_strat.sum() > 0:
                correct_strat = (df_eval[mask_strat]['prediction_numeric'] == df_eval[mask_strat]['target_banker_player']).sum()
                total_strat = mask_strat.sum()
                perf_par_strategie[strategie] = {
                    'accuracy': (correct_strat / total_strat) * 100,
                    'total': total_strat,
                    'correct': correct_strat
                }

        # Analyse par niveau de confiance
        df_eval['confiance_niveau'] = pd.cut(df_eval['score_confiance'],
                                           bins=[0, 2, 5, 10, float('inf')],
                                           labels=['Faible', 'Moyen', 'Élevé', 'Très_Élevé'])

        perf_par_confiance = {}
        for niveau in df_eval['confiance_niveau'].unique():
            if pd.isna(niveau):
                continue
            mask_conf = df_eval['confiance_niveau'] == niveau
            if mask_conf.sum() > 0:
                correct_conf = (df_eval[mask_conf]['prediction_numeric'] == df_eval[mask_conf]['target_banker_player']).sum()
                total_conf = mask_conf.sum()
                perf_par_confiance[str(niveau)] = {
                    'accuracy': (correct_conf / total_conf) * 100,
                    'total': total_conf,
                    'correct': correct_conf
                }

        # Calcul de l'avantage par rapport au hasard (50%)
        avantage_hasard = accuracy - 50.0

        resultats = {
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'accuracy_globale': accuracy,
            'avantage_hasard': avantage_hasard,
            'accuracy_banker': accuracy_banker,
            'accuracy_player': accuracy_player,
            'performance_par_categorie': perf_par_categorie,
            'performance_par_strategie': perf_par_strategie,
            'performance_par_confiance': perf_par_confiance,
            'distribution_predictions': {
                'BANKER': len(perf_banker),
                'PLAYER': len(perf_player)
            },
            'score_confiance_moyen': df_eval['score_confiance'].mean(),
            'score_confiance_median': df_eval['score_confiance'].median()
        }

        return resultats

    def sauvegarder_rapport_performance(self, resultats: Dict) -> str:
        """
        Sauvegarde un rapport détaillé de performance
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_fichier = f"rapport_predicteur_index5_{timestamp}.txt"

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write("RAPPORT PERFORMANCE PRÉDICTEUR INDEX5\n")
            f.write("=" * 50 + "\n")
            f.write(f"Généré le : {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Performance globale
            f.write("PERFORMANCE GLOBALE\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total prédictions : {resultats['total_predictions']:,}\n")
            f.write(f"Prédictions correctes : {resultats['predictions_correctes']:,}\n")
            f.write(f"Accuracy globale : {resultats['accuracy_globale']:.2f}%\n")
            f.write(f"Avantage vs hasard : {resultats['avantage_hasard']:+.2f}%\n")
            f.write(f"Accuracy BANKER : {resultats['accuracy_banker']:.2f}%\n")
            f.write(f"Accuracy PLAYER : {resultats['accuracy_player']:.2f}%\n\n")

            # Performance par catégorie
            f.write("PERFORMANCE PAR CATÉGORIE DE CARTES\n")
            f.write("-" * 40 + "\n")
            for cat, perf in resultats['performance_par_categorie'].items():
                f.write(f"Catégorie {cat} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Performance par stratégie
            f.write("PERFORMANCE PAR STRATÉGIE\n")
            f.write("-" * 30 + "\n")
            for strat, perf in resultats['performance_par_strategie'].items():
                f.write(f"{strat} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Performance par niveau de confiance
            f.write("PERFORMANCE PAR NIVEAU DE CONFIANCE\n")
            f.write("-" * 40 + "\n")
            for niveau, perf in resultats['performance_par_confiance'].items():
                f.write(f"{niveau} : {perf['accuracy']:.2f}% ({perf['correct']:,}/{perf['total']:,})\n")
            f.write("\n")

            # Distribution
            f.write("DISTRIBUTION DES PRÉDICTIONS\n")
            f.write("-" * 35 + "\n")
            f.write(f"BANKER : {resultats['distribution_predictions']['BANKER']:,}\n")
            f.write(f"PLAYER : {resultats['distribution_predictions']['PLAYER']:,}\n")
            f.write(f"Score confiance moyen : {resultats['score_confiance_moyen']:.2f}\n")
            f.write(f"Score confiance médian : {resultats['score_confiance_median']:.2f}\n")

        print(f"📄 Rapport sauvegardé : {nom_fichier}")
        return nom_fichier

def main():
    """
    Fonction principale du prédicteur
    """
    try:
        print("🚀 DÉMARRAGE PRÉDICTEUR INDEX5")
        print("=" * 50)
        
        # Initialisation
        predicteur = PredicteurIndex5()
        
        # Chargement dataset
        dataset = predicteur.charger_dataset()
        
        # Extraction données
        df_donnees = predicteur.extraire_donnees_predictives(dataset)

        # 🚨 RÈGLES INDEX1 INTRINSÈQUES - PAS DE VALIDATION NÉCESSAIRE
        print("✅ Règles de transition INDEX1 : Déterministes par nature du système")
        print("   • C (5 cartes) : INVERSION systématique (0→1, 1→0)")
        print("   • A (4 cartes) : CONSERVATION (0→0, 1→1)")
        print("   • B (6 cartes) : CONSERVATION (0→0, 1→1)")

        # Exécution prédictions
        df_predictions = predicteur.executer_predictions(df_donnees)
        
        # Évaluation
        resultats = predicteur.evaluer_performance(df_predictions)

        # Sauvegarde rapport
        nom_rapport = predicteur.sauvegarder_rapport_performance(resultats)

        # Affichage résultats
        print("\n🎯 RÉSULTATS PRÉDICTEUR INDEX5:")
        print("=" * 50)
        print(f"Total prédictions: {resultats['total_predictions']:,}")
        print(f"Prédictions correctes: {resultats['predictions_correctes']:,}")
        print(f"Accuracy globale: {resultats['accuracy_globale']:.2f}%")
        print(f"Avantage vs hasard: {resultats['avantage_hasard']:+.2f}%")
        print(f"Accuracy BANKER: {resultats['accuracy_banker']:.2f}%")
        print(f"Accuracy PLAYER: {resultats['accuracy_player']:.2f}%")

        print(f"\n📊 Performance par catégorie:")
        for cat, perf in resultats['performance_par_categorie'].items():
            print(f"  Catégorie {cat}: {perf['accuracy']:.2f}% ({perf['total']:,} obs)")

        print(f"\n🎯 Performance par stratégie:")
        for strat, perf in sorted(resultats['performance_par_strategie'].items(),
                                key=lambda x: x[1]['accuracy'], reverse=True):
            print(f"  {strat}: {perf['accuracy']:.2f}% ({perf['total']:,} obs)")

        print(f"\n📈 Distribution prédictions:")
        print(f"  BANKER: {resultats['distribution_predictions']['BANKER']:,}")
        print(f"  PLAYER: {resultats['distribution_predictions']['PLAYER']:,}")

        print(f"\n📊 Scores de confiance:")
        print(f"  Moyen: {resultats['score_confiance_moyen']:.2f}")
        print(f"  Médian: {resultats['score_confiance_median']:.2f}")

        print(f"\n📄 Rapport détaillé: {nom_rapport}")
        print(f"\n✅ PRÉDICTEUR INDEX5 TERMINÉ AVEC SUCCÈS")

        # Analyse de la performance
        if resultats['accuracy_globale'] > 52.0:
            print(f"🎉 EXCELLENT: Accuracy > 52% - Avantage significatif!")
        elif resultats['accuracy_globale'] > 50.5:
            print(f"✅ BON: Accuracy > 50.5% - Avantage détectable")
        else:
            print(f"⚠️  ATTENTION: Accuracy ≤ 50.5% - Revoir stratégies")

        return True
        
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
